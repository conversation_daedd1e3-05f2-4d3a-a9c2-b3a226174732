/**
 * SignupScreen - Comprehensive user registration interface
 * Features: Form validation, password strength, terms acceptance, animations
 */

import React, { useRef, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  KeyboardAvoidingView,
  Platform,
  TouchableOpacity,
  Animated,
  Alert,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Input, Button, Checkbox, LoadingIndicator } from '../components';
import PasswordStrengthIndicator from '../components/PasswordStrengthIndicator';
import { useAuth } from '../hooks';
import { theme } from '../theme';

const SignupScreen = ({ navigation }) => {
  const { signupForm, handleSignup, loading, error, clearError } = useAuth();
  const fadeAnim = useRef(new Animated.Value(0)).current;
  const slideAnim = useRef(new Animated.Value(50)).current;

  // Animate screen entrance
  useEffect(() => {
    Animated.parallel([
      Animated.timing(fadeAnim, {
        toValue: 1,
        duration: theme.animation.slow,
        useNativeDriver: true,
      }),
      Animated.timing(slideAnim, {
        toValue: 0,
        duration: theme.animation.slow,
        useNativeDriver: true,
      }),
    ]).start();
  }, [fadeAnim, slideAnim]);

  // Clear error when component unmounts or form changes
  useEffect(() => {
    return () => clearError();
  }, [clearError]);

  // Handle signup submission
  const onSignupPress = async () => {
    try {
      const result = await handleSignup();
      if (result.isValid) {
        Alert.alert('Success', 'Account created successfully!');
      }
    } catch (error) {
      Alert.alert('Signup Failed', error.message || 'Please try again');
    }
  };

  // Navigate to login screen
  const onLoginPress = () => {
    navigation.navigate('Login');
  };

  // Handle terms and conditions press
  const onTermsPress = () => {
    Alert.alert(
      'Terms & Conditions',
      'Terms and conditions content would be displayed here',
      [{ text: 'OK' }]
    );
  };

  return (
    <SafeAreaView style={styles.container}>
      <KeyboardAvoidingView
        style={styles.keyboardAvoid}
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
      >
        <ScrollView
          style={styles.scrollView}
          contentContainerStyle={styles.scrollContent}
          keyboardShouldPersistTaps="handled"
          showsVerticalScrollIndicator={false}
        >
          <Animated.View
            style={[
              styles.content,
              {
                opacity: fadeAnim,
                transform: [{ translateY: slideAnim }],
              },
            ]}
          >
            {/* Header */}
            <View style={styles.header}>
              <Text style={styles.title}>Create Account</Text>
              <Text style={styles.subtitle}>
                Join us today and get started
              </Text>
            </View>

            {/* Error Message */}
            {error && (
              <View style={styles.errorContainer}>
                <Text style={styles.errorText}>{error}</Text>
              </View>
            )}

            {/* Signup Form */}
            <View style={styles.form}>
              {/* Full Name Input */}
              <Input
                label="Full Name"
                placeholder="Enter your full name"
                autoCapitalize="words"
                autoComplete="name"
                {...signupForm.getFieldProps('fullName')}
              />

              {/* Email Input */}
              <Input
                label="Email Address"
                placeholder="Enter your email"
                keyboardType="email-address"
                autoCapitalize="none"
                autoComplete="email"
                {...signupForm.getFieldProps('email')}
              />

              {/* Password Input */}
              <Input
                label="Password"
                placeholder="Create a strong password"
                secureTextEntry
                showPasswordToggle
                autoComplete="new-password"
                {...signupForm.getFieldProps('password')}
              />

              {/* Password Strength Indicator */}
              <PasswordStrengthIndicator
                password={signupForm.values.password}
              />

              {/* Confirm Password Input */}
              <Input
                label="Confirm Password"
                placeholder="Confirm your password"
                secureTextEntry
                showPasswordToggle
                autoComplete="new-password"
                {...signupForm.getFieldProps('confirmPassword')}
              />

              {/* Terms and Conditions Checkbox */}
              <View style={styles.termsContainer}>
                <Checkbox
                  checked={signupForm.values.acceptTerms}
                  onPress={() =>
                    signupForm.setValue('acceptTerms', !signupForm.values.acceptTerms)
                  }
                  error={signupForm.errors.acceptTerms}
                />
                <View style={styles.termsTextContainer}>
                  <Text style={styles.termsText}>
                    I agree to the{' '}
                  </Text>
                  <TouchableOpacity onPress={onTermsPress}>
                    <Text style={styles.termsLink}>
                      Terms & Conditions
                    </Text>
                  </TouchableOpacity>
                  <Text style={styles.termsText}>
                    {' '}and{' '}
                  </Text>
                  <TouchableOpacity onPress={onTermsPress}>
                    <Text style={styles.termsLink}>
                      Privacy Policy
                    </Text>
                  </TouchableOpacity>
                </View>
              </View>

              {/* Terms Error */}
              {signupForm.errors.acceptTerms && (
                <Text style={styles.termsError}>
                  {signupForm.errors.acceptTerms}
                </Text>
              )}

              {/* Signup Button */}
              <Button
                title="Create Account"
                onPress={onSignupPress}
                loading={loading}
                disabled={!signupForm.canSubmit || !signupForm.values.acceptTerms}
                style={styles.signupButton}
              />
            </View>

            {/* Login Link */}
            <View style={styles.loginContainer}>
              <Text style={styles.loginText}>
                Already have an account?{' '}
              </Text>
              <TouchableOpacity onPress={onLoginPress}>
                <Text style={styles.loginLink}>Sign In</Text>
              </TouchableOpacity>
            </View>
          </Animated.View>
        </ScrollView>
      </KeyboardAvoidingView>

      {/* Loading Overlay */}
      {loading && (
        <LoadingIndicator
          overlay
          visible={loading}
          text="Creating your account..."
        />
      )}
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: theme.colors.neutral[0],
  },
  keyboardAvoid: {
    flex: 1,
  },
  scrollView: {
    flex: 1,
  },
  scrollContent: {
    flexGrow: 1,
    justifyContent: 'center',
    paddingHorizontal: theme.spacing.component.screenPadding,
    paddingVertical: theme.spacing[8],
  },
  content: {
    width: '100%',
    maxWidth: 400,
    alignSelf: 'center',
  },
  header: {
    alignItems: 'center',
    marginBottom: theme.spacing[8],
  },
  title: {
    ...theme.typography.textStyles.h1,
    color: theme.colors.neutral[900],
    marginBottom: theme.spacing[2],
    textAlign: 'center',
  },
  subtitle: {
    ...theme.typography.textStyles.body,
    color: theme.colors.neutral[600],
    textAlign: 'center',
    lineHeight: 24,
  },
  errorContainer: {
    backgroundColor: theme.colors.error[50],
    borderColor: theme.colors.error[200],
    borderWidth: 1,
    borderRadius: theme.borderRadius.md,
    padding: theme.spacing[4],
    marginBottom: theme.spacing[6],
  },
  errorText: {
    ...theme.typography.textStyles.bodySmall,
    color: theme.colors.error[700],
    textAlign: 'center',
  },
  form: {
    marginBottom: theme.spacing[6],
  },
  termsContainer: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    marginVertical: theme.spacing[4],
  },
  termsTextContainer: {
    flex: 1,
    flexDirection: 'row',
    flexWrap: 'wrap',
    alignItems: 'center',
  },
  termsText: {
    ...theme.typography.textStyles.bodySmall,
    color: theme.colors.neutral[600],
    lineHeight: 20,
  },
  termsLink: {
    ...theme.typography.textStyles.bodySmall,
    color: theme.colors.primary[600],
    fontWeight: theme.typography.fontWeight.semiBold,
    textDecorationLine: 'underline',
  },
  termsError: {
    ...theme.typography.textStyles.inputError,
    color: theme.colors.error[500],
    marginTop: theme.spacing[1],
    marginLeft: theme.spacing[1],
  },
  signupButton: {
    marginTop: theme.spacing[6],
  },
  loginContainer: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    paddingTop: theme.spacing[6],
    borderTopWidth: 1,
    borderTopColor: theme.colors.neutral[200],
  },
  loginText: {
    ...theme.typography.textStyles.body,
    color: theme.colors.neutral[600],
  },
  loginLink: {
    ...theme.typography.textStyles.body,
    color: theme.colors.primary[600],
    fontWeight: theme.typography.fontWeight.semiBold,
  },
});

export default SignupScreen;
