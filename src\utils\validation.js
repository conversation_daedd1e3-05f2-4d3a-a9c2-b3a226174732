/**
 * Validation Utilities - Comprehensive form validation functions
 * Features: Email, password, name validation with detailed error messages
 */

// Email validation regex - RFC 5322 compliant
const EMAIL_REGEX = /^[a-zA-Z0-9.!#$%&'*+/=?^_`{|}~-]+@[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(?:\.[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)*$/;

// Password strength regex patterns
const PASSWORD_PATTERNS = {
  minLength: /.{8,}/,
  hasUppercase: /[A-Z]/,
  hasLowercase: /[a-z]/,
  hasNumber: /\d/,
  hasSpecialChar: /[!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?]/,
};

// Name validation regex - letters, spaces, hyphens, apostrophes
const NAME_REGEX = /^[a-zA-Z\s\-']+$/;

/**
 * Validate email address
 * @param {string} email - Email to validate
 * @returns {object} - Validation result with isValid and error message
 */
export const validateEmail = (email) => {
  if (!email || email.trim() === '') {
    return {
      isValid: false,
      error: 'Email is required',
    };
  }

  if (!EMAIL_REGEX.test(email.trim())) {
    return {
      isValid: false,
      error: 'Please enter a valid email address',
    };
  }

  return {
    isValid: true,
    error: null,
  };
};

/**
 * Validate password with strength requirements
 * @param {string} password - Password to validate
 * @returns {object} - Validation result with isValid, error, and strength info
 */
export const validatePassword = (password) => {
  if (!password) {
    return {
      isValid: false,
      error: 'Password is required',
      strength: 'weak',
      requirements: {
        minLength: false,
        hasUppercase: false,
        hasLowercase: false,
        hasNumber: false,
        hasSpecialChar: false,
      },
    };
  }

  const requirements = {
    minLength: PASSWORD_PATTERNS.minLength.test(password),
    hasUppercase: PASSWORD_PATTERNS.hasUppercase.test(password),
    hasLowercase: PASSWORD_PATTERNS.hasLowercase.test(password),
    hasNumber: PASSWORD_PATTERNS.hasNumber.test(password),
    hasSpecialChar: PASSWORD_PATTERNS.hasSpecialChar.test(password),
  };

  const metRequirements = Object.values(requirements).filter(Boolean).length;
  
  // Determine password strength
  let strength = 'weak';
  if (metRequirements >= 5) {
    strength = 'strong';
  } else if (metRequirements >= 3) {
    strength = 'medium';
  }

  // Check if password meets minimum requirements
  const isValid = requirements.minLength && 
                  requirements.hasUppercase && 
                  requirements.hasLowercase && 
                  requirements.hasNumber && 
                  requirements.hasSpecialChar;

  let error = null;
  if (!isValid) {
    const missingRequirements = [];
    if (!requirements.minLength) missingRequirements.push('at least 8 characters');
    if (!requirements.hasUppercase) missingRequirements.push('one uppercase letter');
    if (!requirements.hasLowercase) missingRequirements.push('one lowercase letter');
    if (!requirements.hasNumber) missingRequirements.push('one number');
    if (!requirements.hasSpecialChar) missingRequirements.push('one special character');

    error = `Password must contain ${missingRequirements.join(', ')}`;
  }

  return {
    isValid,
    error,
    strength,
    requirements,
  };
};

/**
 * Validate password confirmation
 * @param {string} password - Original password
 * @param {string} confirmPassword - Password confirmation
 * @returns {object} - Validation result
 */
export const validatePasswordConfirmation = (password, confirmPassword) => {
  if (!confirmPassword) {
    return {
      isValid: false,
      error: 'Please confirm your password',
    };
  }

  if (password !== confirmPassword) {
    return {
      isValid: false,
      error: 'Passwords do not match',
    };
  }

  return {
    isValid: true,
    error: null,
  };
};

/**
 * Validate full name
 * @param {string} name - Name to validate
 * @returns {object} - Validation result
 */
export const validateName = (name) => {
  if (!name || name.trim() === '') {
    return {
      isValid: false,
      error: 'Full name is required',
    };
  }

  const trimmedName = name.trim();

  if (trimmedName.length < 2) {
    return {
      isValid: false,
      error: 'Name must be at least 2 characters long',
    };
  }

  if (!NAME_REGEX.test(trimmedName)) {
    return {
      isValid: false,
      error: 'Name can only contain letters, spaces, hyphens, and apostrophes',
    };
  }

  // Check if name has at least first and last name
  const nameParts = trimmedName.split(' ').filter(part => part.length > 0);
  if (nameParts.length < 2) {
    return {
      isValid: false,
      error: 'Please enter your full name (first and last name)',
    };
  }

  return {
    isValid: true,
    error: null,
  };
};

/**
 * Validate required field
 * @param {string} value - Value to validate
 * @param {string} fieldName - Name of the field for error message
 * @returns {object} - Validation result
 */
export const validateRequired = (value, fieldName = 'This field') => {
  if (!value || value.toString().trim() === '') {
    return {
      isValid: false,
      error: `${fieldName} is required`,
    };
  }

  return {
    isValid: true,
    error: null,
  };
};

/**
 * Get password strength color
 * @param {string} strength - Password strength (weak, medium, strong)
 * @returns {string} - Color for the strength indicator
 */
export const getPasswordStrengthColor = (strength) => {
  switch (strength) {
    case 'strong':
      return '#10B981'; // Green
    case 'medium':
      return '#F59E0B'; // Orange
    case 'weak':
    default:
      return '#EF4444'; // Red
  }
};

/**
 * Get password strength text
 * @param {string} strength - Password strength (weak, medium, strong)
 * @returns {string} - Human readable strength text
 */
export const getPasswordStrengthText = (strength) => {
  switch (strength) {
    case 'strong':
      return 'Strong password';
    case 'medium':
      return 'Medium strength';
    case 'weak':
    default:
      return 'Weak password';
  }
};

/**
 * Validate entire form
 * @param {object} formData - Form data to validate
 * @param {array} rules - Validation rules
 * @returns {object} - Validation results for all fields
 */
export const validateForm = (formData, rules) => {
  const errors = {};
  let isFormValid = true;

  rules.forEach(rule => {
    const { field, validator, ...options } = rule;
    const value = formData[field];
    
    let result;
    if (typeof validator === 'function') {
      result = validator(value, options);
    } else {
      // Use built-in validators
      switch (validator) {
        case 'email':
          result = validateEmail(value);
          break;
        case 'password':
          result = validatePassword(value);
          break;
        case 'passwordConfirmation':
          result = validatePasswordConfirmation(formData.password, value);
          break;
        case 'name':
          result = validateName(value);
          break;
        case 'required':
          result = validateRequired(value, options.fieldName || field);
          break;
        default:
          result = { isValid: true, error: null };
      }
    }

    if (!result.isValid) {
      errors[field] = result.error;
      isFormValid = false;
    }
  });

  return {
    isValid: isFormValid,
    errors,
  };
};
