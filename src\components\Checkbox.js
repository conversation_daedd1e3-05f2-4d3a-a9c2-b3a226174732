/**
 * Checkbox Component - Reusable checkbox with animations
 * Features: Smooth animations, custom styling, disabled states
 */

import React, { useRef, useEffect } from 'react';
import {
  TouchableOpacity,
  Text,
  View,
  StyleSheet,
  Animated,
} from 'react-native';
import { theme } from '../theme';

const Checkbox = ({
  checked = false,
  onPress,
  label,
  disabled = false,
  error = false,
  style,
  checkboxStyle,
  labelStyle,
  ...props
}) => {
  const scaleValue = useRef(new Animated.Value(checked ? 1 : 0)).current;
  const opacityValue = useRef(new Animated.Value(checked ? 1 : 0)).current;

  // Animate checkbox when checked state changes
  useEffect(() => {
    Animated.parallel([
      Animated.spring(scaleValue, {
        toValue: checked ? 1 : 0,
        useNativeDriver: true,
        tension: 100,
        friction: 8,
      }),
      Animated.timing(opacityValue, {
        toValue: checked ? 1 : 0,
        duration: theme.animation.fast,
        useNativeDriver: true,
      }),
    ]).start();
  }, [checked, scaleValue, opacityValue]);

  // Get checkbox container styles
  const getCheckboxStyles = () => {
    const baseStyles = [styles.checkbox];
    
    if (checked) {
      baseStyles.push(styles.checkboxChecked);
    } else {
      baseStyles.push(styles.checkboxUnchecked);
    }
    
    if (error) {
      baseStyles.push(styles.checkboxError);
    }
    
    if (disabled) {
      baseStyles.push(styles.checkboxDisabled);
    }
    
    return baseStyles;
  };

  // Get label styles
  const getLabelStyles = () => {
    const baseStyles = [styles.label];
    
    if (error) {
      baseStyles.push(styles.labelError);
    }
    
    if (disabled) {
      baseStyles.push(styles.labelDisabled);
    }
    
    return baseStyles;
  };

  return (
    <TouchableOpacity
      style={[styles.container, style]}
      onPress={onPress}
      disabled={disabled}
      activeOpacity={0.7}
      // Accessibility props
      accessible={true}
      accessibilityLabel={label}
      accessibilityRole="checkbox"
      accessibilityState={{
        checked,
        disabled,
      }}
      accessibilityHint={error ? `Error: ${error}` : undefined}
      {...props}
    >
      {/* Checkbox */}
      <View style={[getCheckboxStyles(), checkboxStyle]}>
        <Animated.View
          style={[
            styles.checkmark,
            {
              transform: [{ scale: scaleValue }],
              opacity: opacityValue,
            },
          ]}
        >
          <Text style={styles.checkmarkText}>✓</Text>
        </Animated.View>
      </View>

      {/* Label */}
      {label && (
        <Text style={[getLabelStyles(), labelStyle]}>
          {label}
        </Text>
      )}
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    alignItems: 'center',
    marginVertical: theme.spacing[2],
  },
  checkbox: {
    width: 20,
    height: 20,
    borderRadius: theme.borderRadius.sm,
    borderWidth: 2,
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: theme.spacing[3],
  },
  checkboxUnchecked: {
    borderColor: theme.colors.neutral[400],
    backgroundColor: 'transparent',
  },
  checkboxChecked: {
    borderColor: theme.colors.primary[500],
    backgroundColor: theme.colors.primary[500],
  },
  checkboxError: {
    borderColor: theme.colors.error[500],
  },
  checkboxDisabled: {
    borderColor: theme.colors.neutral[300],
    backgroundColor: theme.colors.neutral[100],
  },
  checkmark: {
    alignItems: 'center',
    justifyContent: 'center',
  },
  checkmarkText: {
    color: theme.colors.neutral[0],
    fontSize: 12,
    fontWeight: theme.typography.fontWeight.bold,
    lineHeight: 12,
  },
  label: {
    flex: 1,
    ...theme.typography.textStyles.body,
    color: theme.colors.neutral[700],
  },
  labelError: {
    color: theme.colors.error[500],
  },
  labelDisabled: {
    color: theme.colors.neutral[400],
  },
});

export default Checkbox;
