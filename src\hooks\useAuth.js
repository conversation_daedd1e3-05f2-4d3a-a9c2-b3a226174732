/**
 * useAuth Hook - Authentication state management
 * Features: Login, signup, form validation, loading states
 */

import { useState, useCallback } from 'react';
import { useForm } from './useForm';

// Login form validation rules
const LOGIN_VALIDATION_RULES = [
  { field: 'email', validator: 'email' },
  { field: 'password', validator: 'required', fieldName: 'Password' },
];

// Signup form validation rules
const SIGNUP_VALIDATION_RULES = [
  { field: 'fullName', validator: 'name' },
  { field: 'email', validator: 'email' },
  { field: 'password', validator: 'password' },
  { field: 'confirmPassword', validator: 'passwordConfirmation' },
];

export const useAuth = () => {
  const [authState, setAuthState] = useState({
    isAuthenticated: false,
    user: null,
    loading: false,
    error: null,
  });

  // Login form
  const loginForm = useForm(
    {
      email: '',
      password: '',
      rememberMe: false,
    },
    LOGIN_VALIDATION_RULES
  );

  // Signup form
  const signupForm = useForm(
    {
      fullName: '',
      email: '',
      password: '',
      confirmPassword: '',
      acceptTerms: false,
    },
    SIGNUP_VALIDATION_RULES
  );

  // Set loading state
  const setLoading = useCallback((loading) => {
    setAuthState(prev => ({
      ...prev,
      loading,
    }));
  }, []);

  // Set error state
  const setError = useCallback((error) => {
    setAuthState(prev => ({
      ...prev,
      error,
    }));
  }, []);

  // Clear error state
  const clearError = useCallback(() => {
    setAuthState(prev => ({
      ...prev,
      error: null,
    }));
  }, []);

  // Mock login function (replace with actual API call)
  const login = useCallback(async (credentials) => {
    setLoading(true);
    clearError();

    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      // Mock successful login
      const user = {
        id: '1',
        email: credentials.email,
        fullName: 'John Doe',
      };

      setAuthState(prev => ({
        ...prev,
        isAuthenticated: true,
        user,
        loading: false,
      }));

      return { success: true, user };
    } catch (error) {
      setError(error.message || 'Login failed');
      setLoading(false);
      throw error;
    }
  }, [setLoading, clearError, setError]);

  // Mock signup function (replace with actual API call)
  const signup = useCallback(async (userData) => {
    setLoading(true);
    clearError();

    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      // Mock successful signup
      const user = {
        id: '1',
        email: userData.email,
        fullName: userData.fullName,
      };

      setAuthState(prev => ({
        ...prev,
        isAuthenticated: true,
        user,
        loading: false,
      }));

      return { success: true, user };
    } catch (error) {
      setError(error.message || 'Signup failed');
      setLoading(false);
      throw error;
    }
  }, [setLoading, clearError, setError]);

  // Logout function
  const logout = useCallback(() => {
    setAuthState({
      isAuthenticated: false,
      user: null,
      loading: false,
      error: null,
    });
    
    // Reset forms
    loginForm.resetForm();
    signupForm.resetForm();
  }, [loginForm, signupForm]);

  // Handle login form submission
  const handleLogin = useCallback(async () => {
    try {
      const result = await loginForm.handleSubmit(async (values) => {
        await login({
          email: values.email,
          password: values.password,
          rememberMe: values.rememberMe,
        });
      });

      return result;
    } catch (error) {
      console.error('Login error:', error);
      throw error;
    }
  }, [loginForm, login]);

  // Handle signup form submission
  const handleSignup = useCallback(async () => {
    try {
      // Additional validation for terms acceptance
      if (!signupForm.values.acceptTerms) {
        signupForm.setValue('acceptTerms', false);
        return { isValid: false };
      }

      const result = await signupForm.handleSubmit(async (values) => {
        await signup({
          fullName: values.fullName,
          email: values.email,
          password: values.password,
        });
      });

      return result;
    } catch (error) {
      console.error('Signup error:', error);
      throw error;
    }
  }, [signupForm, signup]);

  // Mock forgot password function
  const forgotPassword = useCallback(async (email) => {
    setLoading(true);
    clearError();

    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1500));
      
      setLoading(false);
      return { success: true, message: 'Password reset email sent' };
    } catch (error) {
      setError(error.message || 'Failed to send reset email');
      setLoading(false);
      throw error;
    }
  }, [setLoading, clearError, setError]);

  return {
    // Auth state
    ...authState,
    
    // Forms
    loginForm,
    signupForm,
    
    // Actions
    login,
    signup,
    logout,
    handleLogin,
    handleSignup,
    forgotPassword,
    setError,
    clearError,
  };
};
