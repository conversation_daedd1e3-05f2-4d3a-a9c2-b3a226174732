/**
 * Input Component - Reusable text input with validation states
 * Features: Focus states, error handling, password toggle, animations
 */

import React, { useState, useRef, useEffect } from 'react';
import {
  View,
  TextInput,
  Text,
  TouchableOpacity,
  StyleSheet,
  Animated,
} from 'react-native';
import { theme } from '../theme';
import { shakeAnimation } from '../utils/animations';

const Input = ({
  label,
  value,
  onChangeText,
  placeholder,
  secureTextEntry = false,
  keyboardType = 'default',
  autoCapitalize = 'none',
  error,
  disabled = false,
  showPasswordToggle = false,
  leftIcon,
  rightIcon,
  onFocus,
  onBlur,
  style,
  inputStyle,
  ...props
}) => {
  const [isFocused, setIsFocused] = useState(false);
  const [isPasswordVisible, setIsPasswordVisible] = useState(!secureTextEntry);
  const animatedValue = useRef(new Animated.Value(0)).current;
  const shakeValue = useRef(new Animated.Value(0)).current;
  const inputRef = useRef(null);

  // Trigger shake animation when error appears
  useEffect(() => {
    if (error) {
      shakeAnimation(shakeValue).start();
    }
  }, [error, shakeValue]);

  // Handle focus animation
  const handleFocus = (event) => {
    setIsFocused(true);
    Animated.timing(animatedValue, {
      toValue: 1,
      duration: theme.animation.fast,
      useNativeDriver: false,
    }).start();
    onFocus?.(event);
  };

  // Handle blur animation
  const handleBlur = (event) => {
    setIsFocused(false);
    Animated.timing(animatedValue, {
      toValue: 0,
      duration: theme.animation.fast,
      useNativeDriver: false,
    }).start();
    onBlur?.(event);
  };

  // Toggle password visibility
  const togglePasswordVisibility = () => {
    setIsPasswordVisible(!isPasswordVisible);
  };

  // Determine border color based on state
  const getBorderColor = () => {
    if (error) return theme.colors.error[500];
    if (isFocused) return theme.colors.primary[500];
    return theme.colors.neutral[300];
  };

  // Animated border color
  const animatedBorderColor = animatedValue.interpolate({
    inputRange: [0, 1],
    outputRange: [theme.colors.neutral[300], theme.colors.primary[500]],
  });

  return (
    <Animated.View style={[
      styles.container,
      {
        transform: [{ translateX: shakeValue }],
      },
      style,
    ]}>
      {/* Label */}
      {label && (
        <Text style={[
          styles.label,
          error && styles.labelError,
          isFocused && styles.labelFocused,
        ]}>
          {label}
        </Text>
      )}

      {/* Input Container */}
      <Animated.View style={[
        styles.inputContainer,
        {
          borderColor: error ? theme.colors.error[500] : animatedBorderColor,
        },
        disabled && styles.inputContainerDisabled,
      ]}>
        {/* Left Icon */}
        {leftIcon && (
          <View style={styles.leftIconContainer}>
            {leftIcon}
          </View>
        )}

        {/* Text Input */}
        <TextInput
          ref={inputRef}
          style={[
            styles.input,
            leftIcon && styles.inputWithLeftIcon,
            (rightIcon || showPasswordToggle) && styles.inputWithRightIcon,
            disabled && styles.inputDisabled,
            inputStyle,
          ]}
          value={value}
          onChangeText={onChangeText}
          placeholder={placeholder}
          placeholderTextColor={theme.colors.neutral[400]}
          secureTextEntry={secureTextEntry && !isPasswordVisible}
          keyboardType={keyboardType}
          autoCapitalize={autoCapitalize}
          onFocus={handleFocus}
          onBlur={handleBlur}
          editable={!disabled}
          // Accessibility props
          accessible={true}
          accessibilityLabel={label || placeholder}
          accessibilityHint={error ? `Error: ${error}` : undefined}
          accessibilityState={{
            disabled,
            selected: isFocused,
          }}
          accessibilityRole="text"
          {...props}
        />

        {/* Right Icon or Password Toggle */}
        {(rightIcon || showPasswordToggle) && (
          <TouchableOpacity
            style={styles.rightIconContainer}
            onPress={showPasswordToggle ? togglePasswordVisibility : undefined}
            disabled={!showPasswordToggle}
            // Accessibility props
            accessible={showPasswordToggle}
            accessibilityLabel={showPasswordToggle ?
              (isPasswordVisible ? 'Hide password' : 'Show password') :
              undefined
            }
            accessibilityRole="button"
            accessibilityHint={showPasswordToggle ?
              'Toggles password visibility' :
              undefined
            }
          >
            {showPasswordToggle ? (
              <Text style={styles.passwordToggle}>
                {isPasswordVisible ? '👁️' : '👁️‍🗨️'}
              </Text>
            ) : (
              rightIcon
            )}
          </TouchableOpacity>
        )}
      </Animated.View>

      {/* Error Message */}
      {error && (
        <Text style={styles.errorText}>
          {error}
        </Text>
      )}
    </Animated.View>
  );
};

const styles = StyleSheet.create({
  container: {
    marginBottom: theme.spacing.component.inputMargin,
  },
  label: {
    ...theme.typography.textStyles.inputLabel,
    color: theme.colors.neutral[700],
    marginBottom: theme.spacing.component.labelMargin,
  },
  labelFocused: {
    color: theme.colors.primary[500],
  },
  labelError: {
    color: theme.colors.error[500],
  },
  inputContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    borderWidth: 1,
    borderRadius: theme.borderRadius.input,
    backgroundColor: theme.colors.neutral[0],
    minHeight: 48,
  },
  inputContainerDisabled: {
    backgroundColor: theme.colors.neutral[100],
    borderColor: theme.colors.neutral[200],
  },
  input: {
    flex: 1,
    ...theme.typography.textStyles.input,
    color: theme.colors.neutral[900],
    paddingHorizontal: theme.spacing.component.inputPadding,
    paddingVertical: theme.spacing.component.inputPadding,
  },
  inputWithLeftIcon: {
    paddingLeft: theme.spacing[2],
  },
  inputWithRightIcon: {
    paddingRight: theme.spacing[2],
  },
  inputDisabled: {
    color: theme.colors.neutral[400],
  },
  leftIconContainer: {
    paddingLeft: theme.spacing.component.inputPadding,
    justifyContent: 'center',
    alignItems: 'center',
  },
  rightIconContainer: {
    paddingRight: theme.spacing.component.inputPadding,
    justifyContent: 'center',
    alignItems: 'center',
  },
  passwordToggle: {
    fontSize: 18,
    color: theme.colors.neutral[500],
  },
  errorText: {
    ...theme.typography.textStyles.inputError,
    color: theme.colors.error[500],
    marginTop: theme.spacing[1],
    marginLeft: theme.spacing[1],
  },
});

export default Input;
