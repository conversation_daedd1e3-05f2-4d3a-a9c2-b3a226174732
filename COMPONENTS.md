# Component Documentation

## Core Components

### Input Component

A highly customizable input field with validation, animations, and accessibility support.

#### Props

| Prop | Type | Default | Description |
|------|------|---------|-------------|
| `label` | string | - | Input label text |
| `value` | string | - | Input value |
| `onChangeText` | function | - | Value change handler |
| `placeholder` | string | - | Placeholder text |
| `secureTextEntry` | boolean | false | Hide text for passwords |
| `showPasswordToggle` | boolean | false | Show password visibility toggle |
| `keyboardType` | string | 'default' | Keyboard type |
| `autoCapitalize` | string | 'none' | Auto capitalization |
| `error` | string | - | Error message to display |
| `disabled` | boolean | false | Disable input |
| `leftIcon` | ReactNode | - | Left side icon |
| `rightIcon` | ReactNode | - | Right side icon |

#### Features

- Real-time validation feedback
- Smooth focus animations
- Error shake animation
- Password visibility toggle
- Accessibility support
- Custom styling support

#### Usage

```javascript
import { Input } from '../components';

<Input
  label="Email Address"
  placeholder="Enter your email"
  keyboardType="email-address"
  value={email}
  onChangeText={setEmail}
  error={emailError}
/>
```

### Button Component

A versatile button component with multiple variants, loading states, and animations.

#### Props

| Prop | Type | Default | Description |
|------|------|---------|-------------|
| `title` | string | - | Button text |
| `onPress` | function | - | Press handler |
| `variant` | string | 'primary' | Button variant (primary, secondary, outline, ghost) |
| `size` | string | 'medium' | Button size (small, medium, large) |
| `loading` | boolean | false | Show loading state |
| `disabled` | boolean | false | Disable button |
| `leftIcon` | ReactNode | - | Left side icon |
| `rightIcon` | ReactNode | - | Right side icon |

#### Variants

- **Primary**: Solid background with primary color
- **Secondary**: Solid background with secondary color
- **Outline**: Transparent background with colored border
- **Ghost**: Transparent background, no border

#### Usage

```javascript
import { Button } from '../components';

<Button
  title="Sign In"
  variant="primary"
  size="large"
  loading={isLoading}
  onPress={handleLogin}
/>
```

### Checkbox Component

An animated checkbox with smooth transitions and accessibility support.

#### Props

| Prop | Type | Default | Description |
|------|------|---------|-------------|
| `checked` | boolean | false | Checkbox state |
| `onPress` | function | - | Press handler |
| `label` | string | - | Checkbox label |
| `disabled` | boolean | false | Disable checkbox |
| `error` | boolean | false | Error state |

#### Features

- Smooth check/uncheck animations
- Scale and opacity transitions
- Accessibility support
- Error state styling

#### Usage

```javascript
import { Checkbox } from '../components';

<Checkbox
  checked={acceptTerms}
  onPress={() => setAcceptTerms(!acceptTerms)}
  label="I accept the Terms & Conditions"
/>
```

### LoadingIndicator Component

A flexible loading indicator with multiple variants and overlay support.

#### Props

| Prop | Type | Default | Description |
|------|------|---------|-------------|
| `size` | string | 'medium' | Indicator size (small, medium, large) |
| `color` | string | primary | Indicator color |
| `text` | string | - | Loading text |
| `overlay` | boolean | false | Show as overlay |
| `visible` | boolean | true | Show/hide indicator |

#### Variants

- **Default**: Standard ActivityIndicator
- **Spinner**: Custom rotating spinner
- **Dots**: Animated dots loader

#### Usage

```javascript
import { LoadingIndicator } from '../components';

<LoadingIndicator
  overlay
  visible={loading}
  text="Signing you in..."
/>

// Custom variants
<LoadingIndicator.Spinner size={24} />
<LoadingIndicator.Dots color="#3B82F6" />
```

### PasswordStrengthIndicator Component

A visual password strength indicator with real-time feedback.

#### Props

| Prop | Type | Default | Description |
|------|------|---------|-------------|
| `password` | string | - | Password to analyze |
| `style` | object | - | Custom styles |

#### Features

- Real-time strength calculation
- Animated progress bar
- Requirements checklist
- Color-coded feedback
- Smooth animations

#### Usage

```javascript
import PasswordStrengthIndicator from '../components/PasswordStrengthIndicator';

<PasswordStrengthIndicator
  password={password}
/>
```

## Screen Components

### LoginScreen

The main login interface with form validation and animations.

#### Features

- Email and password inputs
- Remember me checkbox
- Forgot password link
- Navigation to signup
- Loading states
- Error handling
- Smooth animations
- Accessibility support

### SignupScreen

Comprehensive registration interface with password strength validation.

#### Features

- Full name validation
- Email validation
- Password strength indicator
- Password confirmation
- Terms & conditions checkbox
- Navigation to login
- Loading states
- Error handling
- Smooth animations
- Accessibility support

## Custom Hooks

### useForm Hook

A powerful form state management hook with validation.

#### Features

- Form state management
- Real-time validation
- Field-level validation
- Form submission handling
- Error management
- Touch state tracking

#### Usage

```javascript
import { useForm } from '../hooks';

const form = useForm(
  { email: '', password: '' },
  [
    { field: 'email', validator: 'email' },
    { field: 'password', validator: 'required' },
  ]
);

// Get field props
const emailProps = form.getFieldProps('email');
```

### useAuth Hook

Authentication state management with form integration.

#### Features

- Authentication state
- Login/signup forms
- API integration ready
- Loading states
- Error handling
- Form validation

#### Usage

```javascript
import { useAuth } from '../hooks';

const { loginForm, handleLogin, loading, error } = useAuth();
```

## Styling Guidelines

### Theme Usage

Always use theme values for consistency:

```javascript
import { theme } from '../theme';

const styles = StyleSheet.create({
  container: {
    backgroundColor: theme.colors.neutral[0],
    padding: theme.spacing.component.screenPadding,
  },
  text: {
    ...theme.typography.textStyles.body,
    color: theme.colors.neutral[900],
  },
});
```

### Responsive Design

Use theme breakpoints for responsive behavior:

```javascript
const isTablet = width > theme.breakpoints.md;
```

### Accessibility

Always include accessibility props:

```javascript
<TouchableOpacity
  accessible={true}
  accessibilityLabel="Sign In"
  accessibilityRole="button"
  accessibilityHint="Signs you into your account"
>
```

## Animation Guidelines

### Using Animation Utilities

```javascript
import { fadeIn, scaleIn } from '../utils/animations';

// Fade in animation
fadeIn(animatedValue).start();

// Scale in with spring
scaleIn(animatedValue, { tension: 100 }).start();
```

### Custom Animations

```javascript
import { Animated } from 'react-native';
import { theme } from '../theme';

Animated.timing(animatedValue, {
  toValue: 1,
  duration: theme.animation.normal,
  useNativeDriver: true,
}).start();
```

## Best Practices

1. **Always use theme values** for colors, spacing, and typography
2. **Include accessibility props** for all interactive elements
3. **Handle loading and error states** appropriately
4. **Use proper validation** for all form inputs
5. **Test on multiple screen sizes** and orientations
6. **Follow React Native performance guidelines**
7. **Use TypeScript** for better type safety (when converting)
8. **Write tests** for critical functionality
9. **Document component changes** and new features
10. **Follow consistent naming conventions**
