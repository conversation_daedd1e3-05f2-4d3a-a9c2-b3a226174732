/**
 * AppNavigator - Main navigation container
 * Features: Authentication flow, conditional navigation based on auth state
 */

import React from 'react';
import { NavigationContainer } from '@react-navigation/native';
import { createStackNavigator } from '@react-navigation/stack';
import AuthNavigator from './AuthNavigator';
import { useAuth } from '../hooks';
import { View, Text, StyleSheet } from 'react-native';
import { theme } from '../theme';
import { LoadingIndicator } from '../components';

const Stack = createStackNavigator();

// Placeholder for authenticated screens
const AuthenticatedScreen = () => {
  const { user, logout } = useAuth();
  
  return (
    <View style={styles.authenticatedContainer}>
      <Text style={styles.welcomeText}>
        Welcome, {user?.fullName || user?.email}!
      </Text>
      <Text style={styles.successText}>
        Authentication system is working perfectly!
      </Text>
      <Text style={styles.infoText}>
        This is where your main app content would go.
      </Text>
    </View>
  );
};

const AppNavigator = () => {
  const { isAuthenticated, loading } = useAuth();

  // Show loading screen while checking authentication
  if (loading) {
    return (
      <View style={styles.loadingContainer}>
        <LoadingIndicator
          size="large"
          text="Loading..."
          visible={true}
        />
      </View>
    );
  }

  return (
    <NavigationContainer>
      <Stack.Navigator
        screenOptions={{
          headerShown: false,
          animationEnabled: true,
        }}
      >
        {isAuthenticated ? (
          <Stack.Screen
            name="Authenticated"
            component={AuthenticatedScreen}
            options={{
              animationTypeForReplace: 'push',
            }}
          />
        ) : (
          <Stack.Screen
            name="Auth"
            component={AuthNavigator}
            options={{
              animationTypeForReplace: 'pop',
            }}
          />
        )}
      </Stack.Navigator>
    </NavigationContainer>
  );
};

const styles = StyleSheet.create({
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: theme.colors.neutral[0],
  },
  authenticatedContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: theme.colors.neutral[0],
    paddingHorizontal: theme.spacing.component.screenPadding,
  },
  welcomeText: {
    ...theme.typography.textStyles.h2,
    color: theme.colors.neutral[900],
    textAlign: 'center',
    marginBottom: theme.spacing[4],
  },
  successText: {
    ...theme.typography.textStyles.body,
    color: theme.colors.success[600],
    textAlign: 'center',
    marginBottom: theme.spacing[6],
    fontWeight: theme.typography.fontWeight.semiBold,
  },
  infoText: {
    ...theme.typography.textStyles.bodySmall,
    color: theme.colors.neutral[600],
    textAlign: 'center',
    lineHeight: 20,
  },
});

export default AppNavigator;
