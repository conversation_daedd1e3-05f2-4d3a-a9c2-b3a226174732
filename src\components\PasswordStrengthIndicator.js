/**
 * PasswordStrengthIndicator - Visual password strength feedback
 * Features: Real-time strength calculation, animated progress bar, requirement checklist
 */

import React, { useRef, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  Animated,
} from 'react-native';
import { theme } from '../theme';
import { validatePassword, getPasswordStrengthColor, getPasswordStrengthText } from '../utils/validation';

const PasswordStrengthIndicator = ({ password, style }) => {
  const progressAnim = useRef(new Animated.Value(0)).current;
  const validation = validatePassword(password);
  
  // Calculate progress percentage
  const getProgressPercentage = () => {
    const metRequirements = Object.values(validation.requirements).filter(Boolean).length;
    return (metRequirements / 5) * 100;
  };

  // Animate progress bar
  useEffect(() => {
    const targetProgress = getProgressPercentage();
    Animated.timing(progressAnim, {
      toValue: targetProgress,
      duration: theme.animation.normal,
      useNativeDriver: false,
    }).start();
  }, [password, progressAnim]);

  // Don't show indicator if password is empty
  if (!password) {
    return null;
  }

  const strengthColor = getPasswordStrengthColor(validation.strength);
  const strengthText = getPasswordStrengthText(validation.strength);

  return (
    <View style={[styles.container, style]}>
      {/* Strength Label and Progress Bar */}
      <View style={styles.strengthHeader}>
        <Text style={[styles.strengthText, { color: strengthColor }]}>
          {strengthText}
        </Text>
        <View style={styles.progressBarContainer}>
          <Animated.View
            style={[
              styles.progressBar,
              {
                backgroundColor: strengthColor,
                width: progressAnim.interpolate({
                  inputRange: [0, 100],
                  outputRange: ['0%', '100%'],
                  extrapolate: 'clamp',
                }),
              },
            ]}
          />
        </View>
      </View>

      {/* Requirements Checklist */}
      <View style={styles.requirements}>
        <RequirementItem
          met={validation.requirements.minLength}
          text="At least 8 characters"
        />
        <RequirementItem
          met={validation.requirements.hasUppercase}
          text="One uppercase letter"
        />
        <RequirementItem
          met={validation.requirements.hasLowercase}
          text="One lowercase letter"
        />
        <RequirementItem
          met={validation.requirements.hasNumber}
          text="One number"
        />
        <RequirementItem
          met={validation.requirements.hasSpecialChar}
          text="One special character"
        />
      </View>
    </View>
  );
};

// Individual requirement item component
const RequirementItem = ({ met, text }) => {
  const scaleAnim = useRef(new Animated.Value(0.8)).current;
  const opacityAnim = useRef(new Animated.Value(0.5)).current;

  useEffect(() => {
    Animated.parallel([
      Animated.spring(scaleAnim, {
        toValue: met ? 1 : 0.8,
        useNativeDriver: true,
        tension: 100,
        friction: 8,
      }),
      Animated.timing(opacityAnim, {
        toValue: met ? 1 : 0.5,
        duration: theme.animation.fast,
        useNativeDriver: true,
      }),
    ]).start();
  }, [met, scaleAnim, opacityAnim]);

  return (
    <Animated.View
      style={[
        styles.requirementItem,
        {
          opacity: opacityAnim,
          transform: [{ scale: scaleAnim }],
        },
      ]}
    >
      <View style={[
        styles.requirementIcon,
        { backgroundColor: met ? theme.colors.success[500] : theme.colors.neutral[300] }
      ]}>
        <Text style={[
          styles.requirementIconText,
          { color: met ? theme.colors.neutral[0] : theme.colors.neutral[500] }
        ]}>
          {met ? '✓' : '○'}
        </Text>
      </View>
      <Text style={[
        styles.requirementText,
        { color: met ? theme.colors.neutral[700] : theme.colors.neutral[500] }
      ]}>
        {text}
      </Text>
    </Animated.View>
  );
};

const styles = StyleSheet.create({
  container: {
    marginTop: theme.spacing[2],
    marginBottom: theme.spacing[4],
  },
  strengthHeader: {
    marginBottom: theme.spacing[3],
  },
  strengthText: {
    ...theme.typography.textStyles.caption,
    fontWeight: theme.typography.fontWeight.semiBold,
    marginBottom: theme.spacing[2],
  },
  progressBarContainer: {
    height: 4,
    backgroundColor: theme.colors.neutral[200],
    borderRadius: theme.borderRadius.full,
    overflow: 'hidden',
  },
  progressBar: {
    height: '100%',
    borderRadius: theme.borderRadius.full,
  },
  requirements: {
    gap: theme.spacing[2],
  },
  requirementItem: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  requirementIcon: {
    width: 16,
    height: 16,
    borderRadius: theme.borderRadius.full,
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: theme.spacing[2],
  },
  requirementIconText: {
    fontSize: 10,
    fontWeight: theme.typography.fontWeight.bold,
    lineHeight: 10,
  },
  requirementText: {
    ...theme.typography.textStyles.caption,
    flex: 1,
  },
});

export default PasswordStrengthIndicator;
