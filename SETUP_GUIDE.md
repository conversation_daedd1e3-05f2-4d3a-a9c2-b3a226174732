# Complete Setup Guide - React Native Authentication System

This guide will walk you through setting up the React Native Authentication System from scratch.

## 📋 Prerequisites

### System Requirements

- **Node.js**: Version 18 or higher
- **npm**: Version 8 or higher (comes with Node.js)
- **React Native CLI**: Latest version
- **Git**: For version control

### Development Environment

#### For Android Development
- **Android Studio**: Latest version
- **Android SDK**: API level 31 or higher
- **Java Development Kit (JDK)**: Version 11 or higher

#### For iOS Development (macOS only)
- **Xcode**: Latest version
- **iOS Simulator**: Included with Xcode
- **CocoaPods**: For iOS dependency management

## 🚀 Step-by-Step Setup

### Step 1: Environment Verification

First, verify your development environment:

```bash
# Check Node.js version
node --version
# Should output v18.x.x or higher

# Check npm version
npm --version
# Should output 8.x.x or higher

# Check React Native CLI
npx react-native --version
# Should output React Native CLI version
```

### Step 2: Project Installation

1. **Navigate to your project directory**
   ```bash
   cd /path/to/your/project
   ```

2. **Install dependencies**
   ```bash
   npm install
   ```

3. **Verify package.json dependencies**
   Ensure these packages are installed:
   ```json
   {
     "dependencies": {
       "react": "18.2.0",
       "react-native": "0.73.2",
       "@react-navigation/native": "^6.1.9",
       "@react-navigation/stack": "^6.3.20",
       "react-native-screens": "^3.29.0",
       "react-native-safe-area-context": "^4.8.2",
       "react-native-gesture-handler": "^2.14.1",
       "react-native-reanimated": "^3.6.2",
       "react-native-vector-icons": "^10.0.3"
     }
   }
   ```

### Step 3: Platform-Specific Setup

#### Android Setup

1. **Open Android Studio**
   - Install Android SDK Platform 31 or higher
   - Install Android SDK Build-Tools
   - Create an Android Virtual Device (AVD)

2. **Set environment variables** (add to your shell profile):
   ```bash
   export ANDROID_HOME=$HOME/Library/Android/sdk
   export PATH=$PATH:$ANDROID_HOME/emulator
   export PATH=$PATH:$ANDROID_HOME/tools
   export PATH=$PATH:$ANDROID_HOME/tools/bin
   export PATH=$PATH:$ANDROID_HOME/platform-tools
   ```

3. **Verify Android setup**
   ```bash
   npx react-native doctor
   ```

#### iOS Setup (macOS only)

1. **Install CocoaPods**
   ```bash
   sudo gem install cocoapods
   ```

2. **Install iOS dependencies**
   ```bash
   cd ios
   pod install
   cd ..
   ```

3. **Open Xcode**
   - Open `ios/authapp.xcworkspace`
   - Select a simulator or connected device
   - Ensure signing is configured

### Step 4: Configuration Files

Verify these configuration files are properly set up:

#### babel.config.js
```javascript
module.exports = {
  presets: ['@react-native/babel-preset'],
  plugins: [
    'react-native-reanimated/plugin',
  ],
};
```

#### metro.config.js
```javascript
const {getDefaultConfig, mergeConfig} = require('@react-native/metro-config');

const config = {};

module.exports = mergeConfig(getDefaultConfig(__dirname), config);
```

#### index.js
```javascript
import 'react-native-gesture-handler';
import {AppRegistry} from 'react-native';
import App from './App';
import {name as appName} from './app.json';

AppRegistry.registerComponent(appName, () => App);
```

### Step 5: Running the Application

1. **Start Metro bundler**
   ```bash
   npm start
   ```

2. **Run on Android** (in a new terminal)
   ```bash
   npm run android
   ```

3. **Run on iOS** (in a new terminal, macOS only)
   ```bash
   npm run ios
   ```

## 🔧 Troubleshooting

### Common Issues and Solutions

#### Metro Bundler Issues

**Problem**: Metro bundler cache issues
```bash
# Solution
npx react-native start --reset-cache
```

**Problem**: Port already in use
```bash
# Solution
npx react-native start --port 8082
```

#### Android Issues

**Problem**: Android build fails
```bash
# Solution 1: Clean build
cd android
./gradlew clean
cd ..

# Solution 2: Reset Metro cache
npx react-native start --reset-cache
```

**Problem**: Unable to load script from assets
```bash
# Solution: Bundle the app
npx react-native bundle --platform android --dev false --entry-file index.js --bundle-output android/app/src/main/assets/index.android.bundle --assets-dest android/app/src/main/res
```

#### iOS Issues

**Problem**: iOS build fails
```bash
# Solution 1: Clean pods
cd ios
rm -rf Pods
rm Podfile.lock
pod install
cd ..

# Solution 2: Clean Xcode build
# In Xcode: Product > Clean Build Folder
```

**Problem**: Simulator not found
```bash
# Solution: List available simulators
xcrun simctl list devices
```

#### Dependency Issues

**Problem**: Package conflicts
```bash
# Solution: Clean install
rm -rf node_modules
rm package-lock.json
npm install
```

**Problem**: React Native Reanimated not working
```bash
# Solution: Ensure babel plugin is configured
# Check babel.config.js includes 'react-native-reanimated/plugin'
```

### Performance Optimization

#### For Development

1. **Enable Flipper** (optional debugging tool)
2. **Use React DevTools**
3. **Enable Fast Refresh**

#### For Production

1. **Enable Hermes** (JavaScript engine)
2. **Optimize bundle size**
3. **Enable ProGuard** (Android)

## 📱 Testing on Devices

### Android Device Testing

1. **Enable Developer Options**
   - Go to Settings > About Phone
   - Tap "Build Number" 7 times

2. **Enable USB Debugging**
   - Go to Settings > Developer Options
   - Enable "USB Debugging"

3. **Connect device and run**
   ```bash
   adb devices  # Verify device is connected
   npm run android
   ```

### iOS Device Testing

1. **Connect device to Mac**
2. **Trust the computer** on device
3. **Open Xcode project**
   ```bash
   open ios/authapp.xcworkspace
   ```
4. **Select your device** and run

## 🔐 Environment Configuration

### Development Environment

Create a `.env` file for environment variables:
```
API_BASE_URL=https://api.development.com
DEBUG_MODE=true
```

### Production Environment

For production builds:
```
API_BASE_URL=https://api.production.com
DEBUG_MODE=false
```

## 📊 Monitoring and Analytics

### Crash Reporting

Consider integrating:
- **Crashlytics** (Firebase)
- **Sentry**
- **Bugsnag**

### Performance Monitoring

Consider integrating:
- **Firebase Performance**
- **New Relic**
- **AppDynamics**

## 🚀 Deployment

### Android Deployment

1. **Generate signed APK**
   ```bash
   cd android
   ./gradlew assembleRelease
   ```

2. **Upload to Google Play Console**

### iOS Deployment

1. **Archive in Xcode**
   - Product > Archive

2. **Upload to App Store Connect**

## 📚 Additional Resources

- [React Native Documentation](https://reactnative.dev/docs/getting-started)
- [React Navigation Documentation](https://reactnavigation.org/docs/getting-started)
- [React Native Reanimated Documentation](https://docs.swmansion.com/react-native-reanimated/)
- [Android Developer Guide](https://developer.android.com/guide)
- [iOS Developer Guide](https://developer.apple.com/documentation/)

## 🆘 Getting Help

If you encounter issues:

1. **Check the troubleshooting section** above
2. **Search existing issues** in the project repository
3. **Create a new issue** with detailed information
4. **Join the community** discussions

## ✅ Verification Checklist

After setup, verify everything works:

- [ ] Project builds successfully on Android
- [ ] Project builds successfully on iOS (if on macOS)
- [ ] Navigation between screens works
- [ ] Form validation works correctly
- [ ] Animations are smooth
- [ ] No console errors or warnings
- [ ] Accessibility features work with screen reader
- [ ] App works on different screen sizes

Congratulations! Your React Native Authentication System is now ready for development and customization.
