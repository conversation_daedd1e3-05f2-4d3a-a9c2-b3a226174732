/**
 * LoadingIndicator Component - Reusable loading indicators
 * Features: Multiple variants, custom colors, overlay support
 */

import React, { useRef, useEffect } from 'react';
import {
  View,
  ActivityIndicator,
  Text,
  StyleSheet,
  Animated,
  Dimensions,
} from 'react-native';
import { theme } from '../theme';

const { width: screenWidth, height: screenHeight } = Dimensions.get('window');

const LoadingIndicator = ({
  size = 'medium', // small, medium, large
  color = theme.colors.primary[500],
  text,
  overlay = false,
  visible = true,
  style,
  ...props
}) => {
  const fadeValue = useRef(new Animated.Value(0)).current;
  const scaleValue = useRef(new Animated.Value(0.8)).current;

  // Animate loading indicator appearance
  useEffect(() => {
    if (visible) {
      Animated.parallel([
        Animated.timing(fadeValue, {
          toValue: 1,
          duration: theme.animation.normal,
          useNativeDriver: true,
        }),
        Animated.spring(scaleValue, {
          toValue: 1,
          useNativeDriver: true,
          tension: 100,
          friction: 8,
        }),
      ]).start();
    } else {
      Animated.parallel([
        Animated.timing(fadeValue, {
          toValue: 0,
          duration: theme.animation.fast,
          useNativeDriver: true,
        }),
        Animated.spring(scaleValue, {
          toValue: 0.8,
          useNativeDriver: true,
          tension: 100,
          friction: 8,
        }),
      ]).start();
    }
  }, [visible, fadeValue, scaleValue]);

  // Get size value for ActivityIndicator
  const getSize = () => {
    switch (size) {
      case 'small':
        return 'small';
      case 'large':
        return 'large';
      default:
        return 'large';
    }
  };

  // Get container styles
  const getContainerStyles = () => {
    const baseStyles = [styles.container];
    
    if (overlay) {
      baseStyles.push(styles.overlay);
    }
    
    return baseStyles;
  };

  if (!visible) {
    return null;
  }

  return (
    <Animated.View
      style={[
        getContainerStyles(),
        {
          opacity: fadeValue,
          transform: [{ scale: scaleValue }],
        },
        style,
      ]}
      {...props}
    >
      <View style={styles.content}>
        <ActivityIndicator
          size={getSize()}
          color={color}
          style={styles.indicator}
        />
        {text && (
          <Text style={[styles.text, { color }]}>
            {text}
          </Text>
        )}
      </View>
    </Animated.View>
  );
};

// Spinner component with custom animation
const Spinner = ({
  size = 24,
  color = theme.colors.primary[500],
  style,
}) => {
  const rotateValue = useRef(new Animated.Value(0)).current;

  useEffect(() => {
    const startRotation = () => {
      Animated.loop(
        Animated.timing(rotateValue, {
          toValue: 1,
          duration: 1000,
          useNativeDriver: true,
        })
      ).start();
    };

    startRotation();
  }, [rotateValue]);

  const rotate = rotateValue.interpolate({
    inputRange: [0, 1],
    outputRange: ['0deg', '360deg'],
  });

  return (
    <Animated.View
      style={[
        styles.spinner,
        {
          width: size,
          height: size,
          borderColor: `${color}20`,
          borderTopColor: color,
          transform: [{ rotate }],
        },
        style,
      ]}
    />
  );
};

// Dots loading animation
const DotsLoader = ({
  color = theme.colors.primary[500],
  size = 8,
  style,
}) => {
  const dot1 = useRef(new Animated.Value(0)).current;
  const dot2 = useRef(new Animated.Value(0)).current;
  const dot3 = useRef(new Animated.Value(0)).current;

  useEffect(() => {
    const animateDots = () => {
      const createAnimation = (dot, delay) => {
        return Animated.loop(
          Animated.sequence([
            Animated.delay(delay),
            Animated.timing(dot, {
              toValue: 1,
              duration: 400,
              useNativeDriver: true,
            }),
            Animated.timing(dot, {
              toValue: 0,
              duration: 400,
              useNativeDriver: true,
            }),
          ])
        );
      };

      Animated.parallel([
        createAnimation(dot1, 0),
        createAnimation(dot2, 200),
        createAnimation(dot3, 400),
      ]).start();
    };

    animateDots();
  }, [dot1, dot2, dot3]);

  const getDotStyle = (animatedValue) => ({
    opacity: animatedValue.interpolate({
      inputRange: [0, 1],
      outputRange: [0.3, 1],
    }),
    transform: [
      {
        scale: animatedValue.interpolate({
          inputRange: [0, 1],
          outputRange: [0.8, 1.2],
        }),
      },
    ],
  });

  return (
    <View style={[styles.dotsContainer, style]}>
      <Animated.View
        style={[
          styles.dot,
          { backgroundColor: color, width: size, height: size },
          getDotStyle(dot1),
        ]}
      />
      <Animated.View
        style={[
          styles.dot,
          { backgroundColor: color, width: size, height: size },
          getDotStyle(dot2),
        ]}
      />
      <Animated.View
        style={[
          styles.dot,
          { backgroundColor: color, width: size, height: size },
          getDotStyle(dot3),
        ]}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    alignItems: 'center',
    justifyContent: 'center',
  },
  overlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: theme.colors.overlay,
    zIndex: theme.zIndex.overlay,
  },
  content: {
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: theme.colors.neutral[0],
    borderRadius: theme.borderRadius.lg,
    padding: theme.spacing[6],
    ...theme.shadows.lg,
  },
  indicator: {
    marginBottom: theme.spacing[2],
  },
  text: {
    ...theme.typography.textStyles.body,
    textAlign: 'center',
  },
  spinner: {
    borderWidth: 2,
    borderRadius: 50,
  },
  dotsContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
  },
  dot: {
    borderRadius: 50,
    marginHorizontal: theme.spacing[1],
  },
});

// Export all loading components
LoadingIndicator.Spinner = Spinner;
LoadingIndicator.Dots = DotsLoader;

export default LoadingIndicator;
