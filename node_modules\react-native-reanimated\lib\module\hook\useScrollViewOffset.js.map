{"version": 3, "names": ["useCallback", "useEffect", "useRef", "logger", "isWeb", "useEvent", "useSharedValue", "IS_WEB", "NOT_INITIALIZED_WARNING", "useScrollViewOffset", "useScrollViewOffsetWeb", "useScrollViewOffsetNative", "animatedRef", "providedOffset", "internalOffset", "offset", "current", "<PERSON><PERSON><PERSON><PERSON>", "element", "getWebScrollableElement", "value", "scrollLeft", "scrollTop", "observe", "tag", "warn", "addEventListener", "removeEventListener", "event", "contentOffset", "x", "y", "scrollNativeEventNames", "workletEventHandler", "registerForEvents", "unregisterFromEvents", "scrollComponent", "getScrollableNode"], "sourceRoot": "../../../src", "sources": ["hook/useScrollViewOffset.ts"], "mappings": "AAAA,YAAY;;AACZ,SAASA,WAAW,EAAEC,SAAS,EAAEC,MAAM,QAAQ,OAAO;AAItD,SAASC,MAAM,QAAQ,oBAAW;AAClC,SAASC,KAAK,QAAQ,uBAAoB;AAO1C,SAASC,QAAQ,QAAQ,eAAY;AACrC,SAASC,cAAc,QAAQ,qBAAkB;AAEjD,MAAMC,MAAM,GAAGH,KAAK,CAAC,CAAC;AAEtB,MAAMI,uBAAuB,GAC3B,qJAAqJ;;AAEvJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMC,mBAAmB,GAAGF,MAAM,GACrCG,sBAAsB,GACtBC,yBAAyB;AAE7B,SAASD,sBAAsBA,CAC7BE,WAAmD,EACnDC,cAAoC,EACf;EACrB,MAAMC,cAAc,GAAGR,cAAc,CAAC,CAAC,CAAC;EACxC,MAAMS,MAAM,GAAGb,MAAM,CAACW,cAAc,IAAIC,cAAc,CAAC,CAACE,OAAO;EAE/D,MAAMC,YAAY,GAAGjB,WAAW,CAAC,MAAM;IACrC,SAAS;;IACT,IAAIY,WAAW,EAAE;MACf,MAAMM,OAAO,GAAGC,uBAAuB,CAACP,WAAW,CAACI,OAAO,CAAC;MAC5D;MACAD,MAAM,CAACK,KAAK,GACVF,OAAO,CAACG,UAAU,KAAK,CAAC,GAAGH,OAAO,CAACI,SAAS,GAAGJ,OAAO,CAACG,UAAU;IACrE;EACF,CAAC,EAAE,CAACT,WAAW,EAAEG,MAAM,CAAC,CAAC;EAEzBd,SAAS,CAAC,MAAM;IACd,IAAI,CAACW,WAAW,EAAE;MAChB;IACF;IAEA,OAAOA,WAAW,CAACW,OAAO,CAAEC,GAAG,IAAK;MAClC,IAAI,CAACA,GAAG,EAAE;QACRrB,MAAM,CAACsB,IAAI,CAACjB,uBAAuB,CAAC;QACpC;MACF;MAEA,MAAMU,OAAO,GAAGC,uBAAuB,CAACP,WAAW,CAACI,OAAO,CAAC;MAC5DE,OAAO,CAACQ,gBAAgB,CAAC,QAAQ,EAAET,YAAY,CAAC;MAEhD,OAAO,MAAM;QACXC,OAAO,CAACS,mBAAmB,CAAC,QAAQ,EAAEV,YAAY,CAAC;MACrD,CAAC;IACH,CAAC,CAAC;EACJ,CAAC,EAAE,CAACL,WAAW,EAAEK,YAAY,CAAC,CAAC;EAE/B,OAAOF,MAAM;AACf;AAEA,SAASJ,yBAAyBA,CAChCC,WAAmD,EACnDC,cAAoC,EACf;EACrB,MAAMC,cAAc,GAAGR,cAAc,CAAC,CAAC,CAAC;EACxC,MAAMS,MAAM,GAAGb,MAAM,CAACW,cAAc,IAAIC,cAAc,CAAC,CAACE,OAAO;EAE/D,MAAMC,YAAY,GAAGZ,QAAQ,CAC1BuB,KAA4B,IAAK;IAChC,SAAS;;IACTb,MAAM,CAACK,KAAK,GACVQ,KAAK,CAACC,aAAa,CAACC,CAAC,KAAK,CAAC,GACvBF,KAAK,CAACC,aAAa,CAACE,CAAC,GACrBH,KAAK,CAACC,aAAa,CAACC,CAAC;EAC7B,CAAC,EACDE;EACA;EACA;EACF,CAA2D;EAE3D/B,SAAS,CAAC,MAAM;IACd,IAAI,CAACW,WAAW,EAAE;MAChB;IACF;IAEA,OAAOA,WAAW,CAACW,OAAO,CAAEC,GAAG,IAAK;MAClC,IAAI,CAACA,GAAG,EAAE;QACRrB,MAAM,CAACsB,IAAI,CAACjB,uBAAuB,CAAC;QACpC;MACF;MAEAS,YAAY,CAACgB,mBAAmB,CAACC,iBAAiB,CAACV,GAAG,CAAC;MACvD,OAAO,MAAM;QACXP,YAAY,CAACgB,mBAAmB,CAACE,oBAAoB,CAACX,GAAG,CAAC;MAC5D,CAAC;IACH,CAAC,CAAC;EACJ,CAAC,EAAE,CAACZ,WAAW,EAAEK,YAAY,CAAC,CAAC;EAE/B,OAAOF,MAAM;AACf;AAEA,SAASI,uBAAuBA,CAC9BiB,eAA0C,EAC7B;EACb,OACGA,eAAe,EAAEC,iBAAiB,CAAC,CAAC,IACrCD,eAAe;AAEnB;AAEA,MAAMJ,sBAAsB,GAAG,CAC7B,UAAU,EACV,mBAAmB,EACnB,iBAAiB,EACjB,uBAAuB,EACvB,qBAAqB,CACtB", "ignoreList": []}