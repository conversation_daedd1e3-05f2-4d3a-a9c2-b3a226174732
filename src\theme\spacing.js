/**
 * Spacing System - 8px Grid System
 * Consistent spacing values for margins, padding, and positioning
 */

export const spacing = {
  // Base unit: 8px
  0: 0,
  1: 4,    // 0.5 * 8px
  2: 8,    // 1 * 8px
  3: 12,   // 1.5 * 8px
  4: 16,   // 2 * 8px
  5: 20,   // 2.5 * 8px
  6: 24,   // 3 * 8px
  7: 28,   // 3.5 * 8px
  8: 32,   // 4 * 8px
  9: 36,   // 4.5 * 8px
  10: 40,  // 5 * 8px
  11: 44,  // 5.5 * 8px
  12: 48,  // 6 * 8px
  14: 56,  // 7 * 8px
  16: 64,  // 8 * 8px
  20: 80,  // 10 * 8px
  24: 96,  // 12 * 8px
  28: 112, // 14 * 8px
  32: 128, // 16 * 8px
  36: 144, // 18 * 8px
  40: 160, // 20 * 8px
  44: 176, // 22 * 8px
  48: 192, // 24 * 8px
  52: 208, // 26 * 8px
  56: 224, // 28 * 8px
  60: 240, // 30 * 8px
  64: 256, // 32 * 8px
  72: 288, // 36 * 8px
  80: 320, // 40 * 8px
  96: 384, // 48 * 8px

  // Semantic spacing values
  xs: 4,
  sm: 8,
  md: 16,
  lg: 24,
  xl: 32,
  '2xl': 40,
  '3xl': 48,
  '4xl': 64,
  '5xl': 80,
  '6xl': 96,

  // Component-specific spacing
  component: {
    // Form elements
    inputPadding: 16,
    inputMargin: 12,
    labelMargin: 8,
    
    // Buttons
    buttonPadding: 16,
    buttonMargin: 12,
    
    // Cards and containers
    cardPadding: 20,
    cardMargin: 16,
    
    // Screen padding
    screenPadding: 20,
    screenMargin: 16,
    
    // Navigation
    headerHeight: 56,
    tabBarHeight: 64,
    
    // Common gaps
    sectionGap: 32,
    itemGap: 16,
    smallGap: 8,
  },
};

// Border radius values following 8px grid
export const borderRadius = {
  none: 0,
  xs: 2,
  sm: 4,
  md: 8,
  lg: 12,
  xl: 16,
  '2xl': 20,
  '3xl': 24,
  full: 9999,
  
  // Component-specific radius
  button: 12,
  input: 8,
  card: 16,
  modal: 20,
  avatar: 9999,
};

// Shadow and elevation values
export const shadows = {
  none: {
    shadowColor: 'transparent',
    shadowOffset: { width: 0, height: 0 },
    shadowOpacity: 0,
    shadowRadius: 0,
    elevation: 0,
  },
  sm: {
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 2,
    elevation: 2,
  },
  md: {
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 4,
  },
  lg: {
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.15,
    shadowRadius: 8,
    elevation: 8,
  },
  xl: {
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 8 },
    shadowOpacity: 0.2,
    shadowRadius: 16,
    elevation: 16,
  },
  '2xl': {
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 12 },
    shadowOpacity: 0.25,
    shadowRadius: 24,
    elevation: 24,
  },
};
