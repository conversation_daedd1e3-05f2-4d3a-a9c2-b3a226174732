/**
 * Button Component - Reusable button with multiple variants and states
 * Features: Loading states, disabled states, different variants, animations
 */

import React, { useRef } from 'react';
import {
  TouchableOpacity,
  Text,
  View,
  ActivityIndicator,
  StyleSheet,
  Animated,
} from 'react-native';
import { theme } from '../theme';

const Button = ({
  title,
  onPress,
  variant = 'primary', // primary, secondary, outline, ghost
  size = 'medium', // small, medium, large
  loading = false,
  disabled = false,
  leftIcon,
  rightIcon,
  style,
  textStyle,
  ...props
}) => {
  const scaleValue = useRef(new Animated.Value(1)).current;

  // Handle press in animation
  const handlePressIn = () => {
    Animated.spring(scaleValue, {
      toValue: 0.95,
      useNativeDriver: true,
    }).start();
  };

  // Handle press out animation
  const handlePressOut = () => {
    Animated.spring(scaleValue, {
      toValue: 1,
      useNativeDriver: true,
    }).start();
  };

  // Get button styles based on variant
  const getButtonStyles = () => {
    const baseStyle = [styles.button, styles[`button${size.charAt(0).toUpperCase() + size.slice(1)}`]];
    
    switch (variant) {
      case 'primary':
        return [
          ...baseStyle,
          styles.buttonPrimary,
          (disabled || loading) && styles.buttonPrimaryDisabled,
        ];
      case 'secondary':
        return [
          ...baseStyle,
          styles.buttonSecondary,
          (disabled || loading) && styles.buttonSecondaryDisabled,
        ];
      case 'outline':
        return [
          ...baseStyle,
          styles.buttonOutline,
          (disabled || loading) && styles.buttonOutlineDisabled,
        ];
      case 'ghost':
        return [
          ...baseStyle,
          styles.buttonGhost,
          (disabled || loading) && styles.buttonGhostDisabled,
        ];
      default:
        return baseStyle;
    }
  };

  // Get text styles based on variant
  const getTextStyles = () => {
    const baseStyle = [styles.buttonText, styles[`buttonText${size.charAt(0).toUpperCase() + size.slice(1)}`]];
    
    switch (variant) {
      case 'primary':
        return [
          ...baseStyle,
          styles.buttonTextPrimary,
          (disabled || loading) && styles.buttonTextPrimaryDisabled,
        ];
      case 'secondary':
        return [
          ...baseStyle,
          styles.buttonTextSecondary,
          (disabled || loading) && styles.buttonTextSecondaryDisabled,
        ];
      case 'outline':
        return [
          ...baseStyle,
          styles.buttonTextOutline,
          (disabled || loading) && styles.buttonTextOutlineDisabled,
        ];
      case 'ghost':
        return [
          ...baseStyle,
          styles.buttonTextGhost,
          (disabled || loading) && styles.buttonTextGhostDisabled,
        ];
      default:
        return baseStyle;
    }
  };

  return (
    <Animated.View style={{ transform: [{ scale: scaleValue }] }}>
      <TouchableOpacity
        style={[getButtonStyles(), style]}
        onPress={onPress}
        onPressIn={handlePressIn}
        onPressOut={handlePressOut}
        disabled={disabled || loading}
        activeOpacity={0.8}
        // Accessibility props
        accessible={true}
        accessibilityLabel={title}
        accessibilityRole="button"
        accessibilityState={{
          disabled: disabled || loading,
          busy: loading,
        }}
        accessibilityHint={loading ? 'Loading, please wait' : undefined}
        {...props}
      >
        {/* Left Icon */}
        {leftIcon && !loading && (
          <View style={styles.leftIcon}>
            {leftIcon}
          </View>
        )}

        {/* Loading Indicator */}
        {loading && (
          <ActivityIndicator
            size="small"
            color={variant === 'primary' ? theme.colors.neutral[0] : theme.colors.primary[500]}
            style={styles.loadingIndicator}
          />
        )}

        {/* Button Text */}
        <Text style={[getTextStyles(), textStyle]}>
          {title}
        </Text>

        {/* Right Icon */}
        {rightIcon && !loading && (
          <View style={styles.rightIcon}>
            {rightIcon}
          </View>
        )}
      </TouchableOpacity>
    </Animated.View>
  );
};

const styles = StyleSheet.create({
  // Base button styles
  button: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    borderRadius: theme.borderRadius.button,
    ...theme.shadows.sm,
  },
  
  // Size variants
  buttonSmall: {
    paddingHorizontal: theme.spacing[4],
    paddingVertical: theme.spacing[2],
    minHeight: 36,
  },
  buttonMedium: {
    paddingHorizontal: theme.spacing[6],
    paddingVertical: theme.spacing[3],
    minHeight: 48,
  },
  buttonLarge: {
    paddingHorizontal: theme.spacing[8],
    paddingVertical: theme.spacing[4],
    minHeight: 56,
  },

  // Primary variant
  buttonPrimary: {
    backgroundColor: theme.colors.primary[500],
  },
  buttonPrimaryDisabled: {
    backgroundColor: theme.colors.neutral[300],
    ...theme.shadows.none,
  },

  // Secondary variant
  buttonSecondary: {
    backgroundColor: theme.colors.secondary[500],
  },
  buttonSecondaryDisabled: {
    backgroundColor: theme.colors.neutral[300],
    ...theme.shadows.none,
  },

  // Outline variant
  buttonOutline: {
    backgroundColor: 'transparent',
    borderWidth: 1,
    borderColor: theme.colors.primary[500],
    ...theme.shadows.none,
  },
  buttonOutlineDisabled: {
    borderColor: theme.colors.neutral[300],
  },

  // Ghost variant
  buttonGhost: {
    backgroundColor: 'transparent',
    ...theme.shadows.none,
  },
  buttonGhostDisabled: {
    backgroundColor: 'transparent',
  },

  // Base text styles
  buttonText: {
    textAlign: 'center',
    fontWeight: theme.typography.fontWeight.semiBold,
  },
  
  // Text size variants
  buttonTextSmall: {
    fontSize: theme.typography.fontSize.sm,
  },
  buttonTextMedium: {
    fontSize: theme.typography.fontSize.base,
  },
  buttonTextLarge: {
    fontSize: theme.typography.fontSize.lg,
  },

  // Text color variants
  buttonTextPrimary: {
    color: theme.colors.neutral[0],
  },
  buttonTextPrimaryDisabled: {
    color: theme.colors.neutral[500],
  },
  buttonTextSecondary: {
    color: theme.colors.neutral[0],
  },
  buttonTextSecondaryDisabled: {
    color: theme.colors.neutral[500],
  },
  buttonTextOutline: {
    color: theme.colors.primary[500],
  },
  buttonTextOutlineDisabled: {
    color: theme.colors.neutral[400],
  },
  buttonTextGhost: {
    color: theme.colors.primary[500],
  },
  buttonTextGhostDisabled: {
    color: theme.colors.neutral[400],
  },

  // Icon styles
  leftIcon: {
    marginRight: theme.spacing[2],
  },
  rightIcon: {
    marginLeft: theme.spacing[2],
  },
  loadingIndicator: {
    marginRight: theme.spacing[2],
  },
});

export default Button;
