/**
 * LoginScreen - Beautiful and functional login interface
 * Features: Form validation, animations, loading states, navigation
 */

import React, { useRef, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  KeyboardAvoidingView,
  Platform,
  TouchableOpacity,
  Animated,
  Alert,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Input, Button, Checkbox, LoadingIndicator } from '../components';
import { useAuth } from '../hooks';
import { theme } from '../theme';

const LoginScreen = ({ navigation }) => {
  const { loginForm, handleLogin, loading, error, clearError } = useAuth();
  const fadeAnim = useRef(new Animated.Value(0)).current;
  const slideAnim = useRef(new Animated.Value(50)).current;

  // Animate screen entrance
  useEffect(() => {
    Animated.parallel([
      Animated.timing(fadeAnim, {
        toValue: 1,
        duration: theme.animation.slow,
        useNativeDriver: true,
      }),
      Animated.timing(slideAnim, {
        toValue: 0,
        duration: theme.animation.slow,
        useNativeDriver: true,
      }),
    ]).start();
  }, [fadeAnim, slideAnim]);

  // Clear error when component unmounts or form changes
  useEffect(() => {
    return () => clearError();
  }, [clearError]);

  // Handle login submission
  const onLoginPress = async () => {
    try {
      const result = await handleLogin();
      if (result.isValid) {
        // Navigation will be handled by the auth state change
        Alert.alert('Success', 'Login successful!');
      }
    } catch (error) {
      Alert.alert('Login Failed', error.message || 'Please try again');
    }
  };

  // Handle forgot password
  const onForgotPasswordPress = () => {
    Alert.alert(
      'Forgot Password',
      'Password reset functionality will be implemented here',
      [{ text: 'OK' }]
    );
  };

  // Navigate to signup screen
  const onSignupPress = () => {
    navigation.navigate('Signup');
  };

  return (
    <SafeAreaView style={styles.container}>
      <KeyboardAvoidingView
        style={styles.keyboardAvoid}
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
      >
        <ScrollView
          style={styles.scrollView}
          contentContainerStyle={styles.scrollContent}
          keyboardShouldPersistTaps="handled"
          showsVerticalScrollIndicator={false}
        >
          <Animated.View
            style={[
              styles.content,
              {
                opacity: fadeAnim,
                transform: [{ translateY: slideAnim }],
              },
            ]}
          >
            {/* Header */}
            <View style={styles.header}>
              <Text
                style={styles.title}
                accessibilityRole="header"
                accessibilityLevel={1}
              >
                Welcome Back
              </Text>
              <Text
                style={styles.subtitle}
                accessibilityRole="text"
              >
                Sign in to your account to continue
              </Text>
            </View>

            {/* Error Message */}
            {error && (
              <View style={styles.errorContainer}>
                <Text style={styles.errorText}>{error}</Text>
              </View>
            )}

            {/* Login Form */}
            <View style={styles.form}>
              {/* Email Input */}
              <Input
                label="Email Address"
                placeholder="Enter your email"
                keyboardType="email-address"
                autoCapitalize="none"
                autoComplete="email"
                {...loginForm.getFieldProps('email')}
              />

              {/* Password Input */}
              <Input
                label="Password"
                placeholder="Enter your password"
                secureTextEntry
                showPasswordToggle
                autoComplete="password"
                {...loginForm.getFieldProps('password')}
              />

              {/* Remember Me & Forgot Password */}
              <View style={styles.formOptions}>
                <Checkbox
                  checked={loginForm.values.rememberMe}
                  onPress={() =>
                    loginForm.setValue('rememberMe', !loginForm.values.rememberMe)
                  }
                  label="Remember me"
                />
                
                <TouchableOpacity
                  onPress={onForgotPasswordPress}
                  style={styles.forgotPasswordButton}
                  accessible={true}
                  accessibilityLabel="Forgot Password"
                  accessibilityRole="button"
                  accessibilityHint="Opens password reset dialog"
                >
                  <Text style={styles.forgotPasswordText}>
                    Forgot Password?
                  </Text>
                </TouchableOpacity>
              </View>

              {/* Login Button */}
              <Button
                title="Sign In"
                onPress={onLoginPress}
                loading={loading}
                disabled={!loginForm.canSubmit}
                style={styles.loginButton}
              />
            </View>

            {/* Signup Link */}
            <View style={styles.signupContainer}>
              <Text style={styles.signupText}>
                Don't have an account?{' '}
              </Text>
              <TouchableOpacity onPress={onSignupPress}>
                <Text style={styles.signupLink}>Sign Up</Text>
              </TouchableOpacity>
            </View>
          </Animated.View>
        </ScrollView>
      </KeyboardAvoidingView>

      {/* Loading Overlay */}
      {loading && (
        <LoadingIndicator
          overlay
          visible={loading}
          text="Signing you in..."
        />
      )}
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: theme.colors.neutral[0],
  },
  keyboardAvoid: {
    flex: 1,
  },
  scrollView: {
    flex: 1,
  },
  scrollContent: {
    flexGrow: 1,
    justifyContent: 'center',
    paddingHorizontal: theme.spacing.component.screenPadding,
    paddingVertical: theme.spacing[8],
  },
  content: {
    width: '100%',
    maxWidth: 400,
    alignSelf: 'center',
  },
  header: {
    alignItems: 'center',
    marginBottom: theme.spacing[10],
  },
  title: {
    ...theme.typography.textStyles.h1,
    color: theme.colors.neutral[900],
    marginBottom: theme.spacing[2],
    textAlign: 'center',
  },
  subtitle: {
    ...theme.typography.textStyles.body,
    color: theme.colors.neutral[600],
    textAlign: 'center',
    lineHeight: 24,
  },
  errorContainer: {
    backgroundColor: theme.colors.error[50],
    borderColor: theme.colors.error[200],
    borderWidth: 1,
    borderRadius: theme.borderRadius.md,
    padding: theme.spacing[4],
    marginBottom: theme.spacing[6],
  },
  errorText: {
    ...theme.typography.textStyles.bodySmall,
    color: theme.colors.error[700],
    textAlign: 'center',
  },
  form: {
    marginBottom: theme.spacing[8],
  },
  formOptions: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: theme.spacing[8],
  },
  forgotPasswordButton: {
    padding: theme.spacing[1],
  },
  forgotPasswordText: {
    ...theme.typography.textStyles.bodySmall,
    color: theme.colors.primary[600],
    fontWeight: theme.typography.fontWeight.medium,
  },
  loginButton: {
    marginTop: theme.spacing[4],
  },
  signupContainer: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    paddingTop: theme.spacing[6],
    borderTopWidth: 1,
    borderTopColor: theme.colors.neutral[200],
  },
  signupText: {
    ...theme.typography.textStyles.body,
    color: theme.colors.neutral[600],
  },
  signupLink: {
    ...theme.typography.textStyles.body,
    color: theme.colors.primary[600],
    fontWeight: theme.typography.fontWeight.semiBold,
  },
});

export default LoginScreen;
