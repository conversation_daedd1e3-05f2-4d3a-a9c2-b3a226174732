{"version": 3, "names": ["useRef", "useState", "getShadowNodeWrapperFromRef", "makeMutable", "isF<PERSON><PERSON>", "isIOS", "isMacOS", "shouldBeUseWeb", "findNodeHandle", "shareableMappingCache", "makeShareableCloneRecursive", "SHOULD_BE_USE_WEB", "getComponentOrScrollable", "component", "getNativeScrollRef", "getScrollableNode", "useAnimatedRefBase", "getWrapper", "observers", "Map", "current", "tagOrWrapperRef", "ref", "fun", "getTag", "size", "currentTag", "for<PERSON>ach", "cleanup", "observer", "set", "observe", "get", "delete", "IS_APPLE", "useAnimatedRefNative", "viewName", "tagOrWrapper", "getTagOrWrapper", "value", "viewConfig", "uiViewClassName", "animatedRefShareableHandle", "__init", "f", "useAnimatedRefWeb", "useAnimatedRef"], "sourceRoot": "../../../src", "sources": ["hook/useAnimatedRef.ts"], "mappings": "AAAA,YAAY;;AAEZ,SAASA,MAAM,EAAEC,QAAQ,QAAQ,OAAO;AAIxC,SAASC,2BAA2B,QAAQ,gBAAgB;AAC5D,SAASC,WAAW,QAAQ,gBAAa;AACzC,SAASC,QAAQ,EAAEC,KAAK,EAAEC,OAAO,EAAEC,cAAc,QAAQ,uBAAoB;AAC7E,SAASC,cAAc,QAAQ,qCAAqC;AACpE,SAASC,qBAAqB,QAAQ,6BAA0B;AAChE,SAASC,2BAA2B,QAAQ,kBAAe;AAQ3D,MAAMC,iBAAiB,GAAGJ,cAAc,CAAC,CAAC;AAY1C,SAASK,wBAAwBA,CAACC,SAAmC,EAAE;EACrE,IAAIA,SAAS,CAACC,kBAAkB,EAAE;IAChC,OAAOD,SAAS,CAACC,kBAAkB,CAAC,CAAC;EACvC;EACA,IAAID,SAAS,CAACE,iBAAiB,EAAE;IAC/B,OAAOF,SAAS,CAACE,iBAAiB,CAAC,CAAC;EACtC;EACA,OAAOF,SAAS;AAClB;AAEA,SAASG,kBAAkBA,CACzBC,UAAwE,EAC/C;EACzB,MAAMC,SAAS,GAAGlB,MAAM,CACtB,IAAImB,GAAG,CAAC,CACV,CAAC,CAACC,OAAO;EACT,MAAMC,eAAe,GAAGrB,MAAM,CAAoC,CAAC,CAAC,CAAC;EAErE,MAAMsB,GAAG,GAAGtB,MAAM,CAAiC,IAAI,CAAC;EAExD,IAAI,CAACsB,GAAG,CAACF,OAAO,EAAE;IAChB,MAAMG,GAA4B,GAChCV,SAAS,IACN;MACH,IAAIA,SAAS,EAAE;QACbQ,eAAe,CAACD,OAAO,GAAGH,UAAU,CAACJ,SAAS,CAAC;;QAE/C;QACAU,GAAG,CAACC,MAAM,GAAG,MAAMhB,cAAc,CAACI,wBAAwB,CAACC,SAAS,CAAC,CAAC;QACtEU,GAAG,CAACH,OAAO,GAAGP,SAAS;QAEvB,IAAIK,SAAS,CAACO,IAAI,EAAE;UAClB,MAAMC,UAAU,GAAGH,GAAG,EAAEC,MAAM,GAAG,CAAC,IAAI,IAAI;UAC1CN,SAAS,CAACS,OAAO,CAAC,CAACC,OAAO,EAAEC,QAAQ,KAAK;YACvC;YACA;YACA;YACA;YACAD,OAAO,GAAG,CAAC;YACXV,SAAS,CAACY,GAAG,CAACD,QAAQ,EAAEA,QAAQ,CAACH,UAAU,CAAC,CAAC;UAC/C,CAAC,CAAC;QACJ;MACF;MAEA,OAAOL,eAAe,CAACD,OAAO;IAChC,CAAE;IAEFG,GAAG,CAACQ,OAAO,GAAIF,QAA6B,IAAK;MAC/C;MACA,MAAMD,OAAO,GAAGC,QAAQ,CAACN,GAAG,EAAEC,MAAM,GAAG,CAAC,IAAI,IAAI,CAAC;MACjDN,SAAS,CAACY,GAAG,CAACD,QAAQ,EAAED,OAAO,CAAC;MAEhC,OAAO,MAAM;QACXV,SAAS,CAACc,GAAG,CAACH,QAAQ,CAAC,GAAG,CAAC;QAC3BX,SAAS,CAACe,MAAM,CAACJ,QAAQ,CAAC;MAC5B,CAAC;IACH,CAAC;IAEDN,GAAG,CAACH,OAAO,GAAG,IAAI;IAClBE,GAAG,CAACF,OAAO,GAAGG,GAAG;EACnB;EAEA,OAAOD,GAAG,CAACF,OAAO;AACpB;AAEA,MAAMc,QAAQ,GAAG7B,KAAK,CAAC,CAAC,IAAIC,OAAO,CAAC,CAAC;AAErC,SAAS6B,oBAAoBA,CAAA,EAEA;EAC3B,MAAM,CAACC,QAAQ,CAAC,GAAGnC,QAAQ,CAAC;EAC1B;EACA,CAACG,QAAQ,CAAC,CAAC,IAAI8B,QAAQ,GAAG/B,WAAW,CAAgB,IAAI,CAAC,GAAG,IAC/D,CAAC;EACD,MAAM,CAACkC,YAAY,CAAC,GAAGpC,QAAQ,CAAC,MAC9BE,WAAW,CAAoC,IAAI,CACrD,CAAC;EAED,MAAMmB,GAAG,GAAGN,kBAAkB,CAAcH,SAAS,IAAK;IACxD,MAAMyB,eAAe,GAAGlC,QAAQ,CAAC,CAAC,GAC9BF,2BAA2B,GAC3BM,cAAc;IAElB6B,YAAY,CAACE,KAAK,GAAGD,eAAe,CAAC1B,wBAAwB,CAACC,SAAS,CAAC,CAAC;IAEzE,IAAIuB,QAAQ,EAAE;MACZA,QAAQ,CAACG,KAAK,GACX1B,SAAS,EAA+B2B,UAAU,EAAEC,eAAe,IACpE,SAAS;IACb;IAEA,OAAOJ,YAAY,CAACE,KAAK;EAC3B,CAAC,CAAC;EAEF,IAAI,CAAC9B,qBAAqB,CAACuB,GAAG,CAACV,GAAG,CAAC,EAAE;IACnC,MAAMoB,0BAA0B,GAAGhC,2BAA2B,CAAC;MAC7DiC,MAAM,EAAEA,CAAA,KAAM;QACZ,SAAS;;QACT,MAAMC,CAAkB,GAAGA,CAAA,KAAMP,YAAY,CAACE,KAAK;QACnD,IAAIH,QAAQ,EAAE;UACZQ,CAAC,CAACR,QAAQ,GAAGA,QAAQ;QACvB;QACA,OAAOQ,CAAC;MACV;IACF,CAAC,CAAC;IACFnC,qBAAqB,CAACqB,GAAG,CAACR,GAAG,EAAEoB,0BAA0B,CAAC;EAC5D;EAEA,OAAOpB,GAAG;AACZ;AAEA,SAASuB,iBAAiBA,CAAA,EAEG;EAC3B,OAAO7B,kBAAkB,CAAcH,SAAS,IAC9CD,wBAAwB,CAACC,SAAS,CACpC,CAAC;AACH;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMiC,cAAc,GAAGnC,iBAAiB,GAC3CkC,iBAAiB,GACjBV,oBAAoB", "ignoreList": []}