/**
 * Theme System - Central theme configuration
 * Combines all design tokens into a cohesive theme system
 */

import { colors } from './colors';
import { typography } from './typography';
import { spacing, borderRadius, shadows } from './spacing';

// Main theme object
export const theme = {
  colors,
  typography,
  spacing,
  borderRadius,
  shadows,
  
  // Animation durations (in milliseconds)
  animation: {
    fast: 150,
    normal: 250,
    slow: 350,
    slower: 500,
  },
  
  // Common component styles
  components: {
    // Screen container
    screen: {
      flex: 1,
      backgroundColor: colors.neutral[0],
      paddingHorizontal: spacing.screenPadding,
    },
    
    // Form container
    form: {
      paddingHorizontal: spacing.component.screenPadding,
      paddingVertical: spacing.component.sectionGap,
    },
    
    // Card container
    card: {
      backgroundColor: colors.neutral[0],
      borderRadius: borderRadius.card,
      padding: spacing.component.cardPadding,
      ...shadows.md,
    },
    
    // Input field base style
    input: {
      borderWidth: 1,
      borderColor: colors.neutral[300],
      borderRadius: borderRadius.input,
      paddingHorizontal: spacing.component.inputPadding,
      paddingVertical: spacing.component.inputPadding,
      fontSize: typography.textStyles.input.fontSize,
      fontWeight: typography.textStyles.input.fontWeight,
      color: colors.neutral[900],
      backgroundColor: colors.neutral[0],
    },
    
    // Button base style
    button: {
      borderRadius: borderRadius.button,
      paddingHorizontal: spacing.component.buttonPadding * 1.5,
      paddingVertical: spacing.component.buttonPadding,
      alignItems: 'center',
      justifyContent: 'center',
      minHeight: 48,
    },
    
    // Text styles with colors
    text: {
      primary: {
        color: colors.neutral[900],
      },
      secondary: {
        color: colors.neutral[600],
      },
      muted: {
        color: colors.neutral[500],
      },
      error: {
        color: colors.error[500],
      },
      success: {
        color: colors.success[500],
      },
      warning: {
        color: colors.warning[500],
      },
    },
  },
  
  // Breakpoints for responsive design
  breakpoints: {
    sm: 576,
    md: 768,
    lg: 992,
    xl: 1200,
  },
  
  // Z-index values
  zIndex: {
    hide: -1,
    auto: 'auto',
    base: 0,
    docked: 10,
    dropdown: 1000,
    sticky: 1100,
    banner: 1200,
    overlay: 1300,
    modal: 1400,
    popover: 1500,
    skipLink: 1600,
    toast: 1700,
    tooltip: 1800,
  },
};

// Theme utilities
export const getColor = (colorPath) => {
  const keys = colorPath.split('.');
  let result = colors;
  
  for (const key of keys) {
    result = result[key];
    if (result === undefined) {
      console.warn(`Color path "${colorPath}" not found in theme`);
      return colors.neutral[500]; // fallback color
    }
  }
  
  return result;
};

export const getSpacing = (value) => {
  if (typeof value === 'number') {
    return value;
  }
  
  const spacingValue = spacing[value];
  if (spacingValue === undefined) {
    console.warn(`Spacing value "${value}" not found in theme`);
    return spacing.md; // fallback spacing
  }
  
  return spacingValue;
};

// Export individual modules for direct access
export { colors, typography, spacing, borderRadius, shadows };

// Default export
export default theme;
