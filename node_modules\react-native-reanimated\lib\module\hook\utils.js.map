{"version": 3, "names": ["isWorkletFunction", "ReanimatedError", "buildWorkletsHash", "worklets", "Object", "values", "reduce", "acc", "worklet", "__workletHash", "toString", "buildDependencies", "dependencies", "handlers", "handlersList", "filter", "handler", "undefined", "push", "areWorkletsEqual", "worklet1", "worklet2", "closure1Keys", "keys", "__closure", "closure2Keys", "length", "every", "key", "areDependenciesEqual", "nextDependencies", "prevDependencies", "is", "x", "y", "Number", "isNaN", "objectIs", "areHookInputsEqual", "nextDeps", "prevDeps", "i", "nextDep", "prevDep", "isAnimated", "prop", "Array", "isArray", "some", "onFrame", "shallowEqual", "a", "b", "a<PERSON><PERSON><PERSON>", "b<PERSON><PERSON><PERSON>", "validateAnimatedStyles", "styles"], "sourceRoot": "../../../src", "sources": ["hook/utils.ts"], "mappings": "AAAA,YAAY;;AAEZ,SAASA,iBAAiB,QAAQ,mBAAgB;AAClD,SAASC,eAAe,QAAQ,cAAW;AAG3C;AACA,OAAO,SAASC,iBAAiBA,CAC/BC,QAEwC,EACxC;EACA;EACA,OAAOC,MAAM,CAACC,MAAM,CAACF,QAAQ,CAAC,CAACG,MAAM,CACnC,CAACC,GAAG,EAAEC,OAA2C,KAC/CD,GAAG,GAAGC,OAAO,CAACC,aAAa,CAACC,QAAQ,CAAC,CAAC,EACxC,EACF,CAAC;AACH;;AAEA;AACA,OAAO,SAASC,iBAAiBA,CAC/BC,YAA4B,EAC5BC,QAAqD,EACrD;EAEA,MAAMC,YAAY,GAAGV,MAAM,CAACC,MAAM,CAACQ,QAAQ,CAAC,CAACE,MAAM,CAChDC,OAAO,IAAKA,OAAO,KAAKC,SAC3B,CAA2B;EAC3B,IAAI,CAACL,YAAY,EAAE;IACjB,OAAOE,YAAY;EACrB;EAEAF,YAAY,CAACM,IAAI,CAAChB,iBAAiB,CAACY,YAAY,CAAC,CAAC;EAClD,OAAOF,YAAY;AACrB;AAEA,SAASO,gBAAgBA,CACvBC,QAAyB,EACzBC,QAAyB,EACzB;EACA,IAAID,QAAQ,CAACX,aAAa,KAAKY,QAAQ,CAACZ,aAAa,EAAE;IACrD,MAAMa,YAAY,GAAGlB,MAAM,CAACmB,IAAI,CAACH,QAAQ,CAACI,SAAS,CAAC;IACpD,MAAMC,YAAY,GAAGrB,MAAM,CAACmB,IAAI,CAACF,QAAQ,CAACG,SAAS,CAAC;IAEpD,OACEF,YAAY,CAACI,MAAM,KAAKD,YAAY,CAACC,MAAM,IAC3CJ,YAAY,CAACK,KAAK,CACfC,GAAG,IACFA,GAAG,IAAIP,QAAQ,CAACG,SAAS,IACzBJ,QAAQ,CAACI,SAAS,CAACI,GAAG,CAAC,KAAKP,QAAQ,CAACG,SAAS,CAACI,GAAG,CACtD,CAAC;EAEL;EAEA,OAAO,KAAK;AACd;;AAEA;AACA,OAAO,SAASC,oBAAoBA,CAClCC,gBAAgC,EAChCC,gBAAgC,EAChC;EACA,SAASC,EAAEA,CAACC,CAAS,EAAEC,CAAS,EAAE;IAChC,OACGD,CAAC,KAAKC,CAAC,KAAKD,CAAC,KAAK,CAAC,IAAI,CAAC,GAAGA,CAAC,KAAK,CAAC,GAAGC,CAAC,CAAC,IACvCC,MAAM,CAACC,KAAK,CAACH,CAAC,CAAC,IAAIE,MAAM,CAACC,KAAK,CAACF,CAAC,CAAE;EAExC;EACA,MAAMG,QAA2D,GAC/D,OAAOjC,MAAM,CAAC4B,EAAE,KAAK,UAAU,GAAG5B,MAAM,CAAC4B,EAAE,GAAGA,EAAE;EAElD,SAASM,kBAAkBA,CACzBC,QAAwB,EACxBC,QAAwB,EACxB;IACA,IAAI,CAACD,QAAQ,IAAI,CAACC,QAAQ,IAAIA,QAAQ,CAACd,MAAM,KAAKa,QAAQ,CAACb,MAAM,EAAE;MACjE,OAAO,KAAK;IACd;IAEA,KAAK,IAAIe,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGD,QAAQ,CAACd,MAAM,EAAE,EAAEe,CAAC,EAAE;MACxC,MAAMC,OAAO,GAAGH,QAAQ,CAACE,CAAC,CAAC;MAC3B,MAAME,OAAO,GAAGH,QAAQ,CAACC,CAAC,CAAC;MAC3B,IAAIJ,QAAQ,CAACK,OAAO,EAAEC,OAAO,CAAC,EAAE;QAC9B;MACF;MACA,IAAI,CAAC3C,iBAAiB,CAAC0C,OAAO,CAAC,IAAI,CAAC1C,iBAAiB,CAAC2C,OAAO,CAAC,EAAE;QAC9D,OAAO,KAAK;MACd;MACA,IAAI,CAACxB,gBAAgB,CAACuB,OAAO,EAAEC,OAAO,CAAC,EAAE;QACvC,OAAO,KAAK;MACd;IACF;IACA,OAAO,IAAI;EACb;EAEA,OAAOL,kBAAkB,CAACR,gBAAgB,EAAEC,gBAAgB,CAAC;AAC/D;AAEA,OAAO,SAASa,UAAUA,CAACC,IAAa,EAAE;EACxC,SAAS;;EACT,IAAIC,KAAK,CAACC,OAAO,CAACF,IAAI,CAAC,EAAE;IACvB,OAAOA,IAAI,CAACG,IAAI,CAACJ,UAAU,CAAC;EAC9B,CAAC,MAAM,IAAI,OAAOC,IAAI,KAAK,QAAQ,IAAIA,IAAI,KAAK,IAAI,EAAE;IACpD,IAAKA,IAAI,CAA6BI,OAAO,KAAKhC,SAAS,EAAE;MAC3D,OAAO,IAAI;IACb,CAAC,MAAM;MACL,OAAOb,MAAM,CAACC,MAAM,CAACwC,IAAI,CAAC,CAACG,IAAI,CAACJ,UAAU,CAAC;IAC7C;EACF;EACA,OAAO,KAAK;AACd;;AAEA;AACA;AACA;AACA,OAAO,SAASM,YAAYA,CAE1BC,CAAI,EAAEC,CAAI,EAAE;EACZ,SAAS;;EACT,MAAMC,KAAK,GAAGjD,MAAM,CAACmB,IAAI,CAAC4B,CAAC,CAAC;EAC5B,MAAMG,KAAK,GAAGlD,MAAM,CAACmB,IAAI,CAAC6B,CAAC,CAAC;EAC5B,IAAIC,KAAK,CAAC3B,MAAM,KAAK4B,KAAK,CAAC5B,MAAM,EAAE;IACjC,OAAO,KAAK;EACd;EACA,KAAK,IAAIe,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGY,KAAK,CAAC3B,MAAM,EAAEe,CAAC,EAAE,EAAE;IACrC,IAAIU,CAAC,CAACE,KAAK,CAACZ,CAAC,CAAC,CAAC,KAAKW,CAAC,CAACC,KAAK,CAACZ,CAAC,CAAC,CAAC,EAAE;MAC/B,OAAO,KAAK;IACd;EACF;EACA,OAAO,IAAI;AACb;AAEA,OAAO,SAASc,sBAAsBA,CAACC,MAA0B,EAAE;EACjE,SAAS;;EACT,IAAI,OAAOA,MAAM,KAAK,QAAQ,EAAE;IAC9B,MAAM,IAAIvD,eAAe,CACvB,uDAAuD,OAAOuD,MAAM,WACtE,CAAC;EACH,CAAC,MAAM,IAAIV,KAAK,CAACC,OAAO,CAACS,MAAM,CAAC,EAAE;IAChC,MAAM,IAAIvD,eAAe,CACvB,4JACF,CAAC;EACH;AACF", "ignoreList": []}