{"version": 3, "names": ["useEffect", "useRef", "isWorkletFunction", "ReanimatedError", "isJest", "isWeb", "makeShareable", "areDependenciesEqual", "buildDependencies", "useHandler", "handlers", "dependencies", "initRef", "current", "context", "savedDependencies", "handler<PERSON>ame", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "useWeb"], "sourceRoot": "../../../src", "sources": ["hook/useHandler.ts"], "mappings": "AAAA,YAAY;;AACZ,SAASA,SAAS,EAAEC,MAAM,QAAQ,OAAO;AAGzC,SAASC,iBAAiB,QAAQ,mBAAgB;AAClD,SAASC,eAAe,QAAQ,cAAW;AAC3C,SAASC,MAAM,EAAEC,KAAK,QAAQ,uBAAoB;AAClD,SAASC,aAAa,QAAQ,kBAAe;AAE7C,SAASC,oBAAoB,EAAEC,iBAAiB,QAAQ,YAAS;;AAmCjE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AASA,OAAO,SAASC,UAAUA,CAIxBC,QAAgD,EAChDC,YAA6B,EACD;EAC5B,MAAMC,OAAO,GAAGX,MAAM,CAA0C,IAAI,CAAC;EACrE,IAAIW,OAAO,CAACC,OAAO,KAAK,IAAI,EAAE;IAC5B,MAAMC,OAAO,GAAGR,aAAa,CAAC,CAAC,CAAY,CAAC;IAC5CM,OAAO,CAACC,OAAO,GAAG;MAChBC,OAAO;MACPC,iBAAiB,EAAE;IACrB,CAAC;EACH;EAEAf,SAAS,CAAC,MAAM;IACd,OAAO,MAAM;MACXY,OAAO,CAACC,OAAO,GAAG,IAAI;IACxB,CAAC;EACH,CAAC,EAAE,EAAE,CAAC;EAEN,MAAM;IAAEC,OAAO;IAAEC;EAAkB,CAAC,GAAGH,OAAO,CAACC,OAAO;EAEtD,KAAK,MAAMG,WAAW,IAAIN,QAAQ,EAAE;IAClC,IAAI,CAACR,iBAAiB,CAACQ,QAAQ,CAACM,WAAW,CAAC,CAAC,EAAE;MAC7C,MAAM,IAAIb,eAAe,CACvB,6EACF,CAAC;IACH;EACF;EAEAQ,YAAY,GAAGH,iBAAiB,CAC9BG,YAAY,EACZD,QACF,CAAC;EAED,MAAMO,oBAAoB,GAAG,CAACV,oBAAoB,CAChDI,YAAY,EACZI,iBACF,CAAC;EACDH,OAAO,CAACC,OAAO,CAACE,iBAAiB,GAAGJ,YAAY;EAChD,MAAMO,MAAM,GAAGb,KAAK,CAAC,CAAC,IAAID,MAAM,CAAC,CAAC;EAElC,OAAO;IAAEU,OAAO;IAAEG,oBAAoB;IAAEC;EAAO,CAAC;AAClD", "ignoreList": []}