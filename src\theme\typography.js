/**
 * Typography System - Modern Authentication App
 * Based on Material Design 3 and iOS Human Interface Guidelines
 */

export const typography = {
  // Font Families
  fontFamily: {
    regular: 'System', // Uses system font (San Francisco on iOS, Roboto on Android)
    medium: 'System',
    semiBold: 'System',
    bold: 'System',
  },

  // Font Weights
  fontWeight: {
    light: '300',
    regular: '400',
    medium: '500',
    semiBold: '600',
    bold: '700',
    extraBold: '800',
  },

  // Font Sizes - Following 8px grid system
  fontSize: {
    xs: 12,    // Extra small text
    sm: 14,    // Small text
    base: 16,  // Base text size
    lg: 18,    // Large text
    xl: 20,    // Extra large
    '2xl': 24, // 2x large
    '3xl': 30, // 3x large
    '4xl': 36, // 4x large
    '5xl': 48, // 5x large
    '6xl': 60, // 6x large
  },

  // Line Heights - Relative to font size
  lineHeight: {
    tight: 1.2,
    normal: 1.4,
    relaxed: 1.6,
    loose: 1.8,
  },

  // Letter Spacing
  letterSpacing: {
    tight: -0.5,
    normal: 0,
    wide: 0.5,
    wider: 1,
  },

  // Text Styles - Pre-defined combinations
  textStyles: {
    // Headings
    h1: {
      fontSize: 36,
      fontWeight: '700',
      lineHeight: 1.2,
      letterSpacing: -0.5,
    },
    h2: {
      fontSize: 30,
      fontWeight: '700',
      lineHeight: 1.2,
      letterSpacing: -0.5,
    },
    h3: {
      fontSize: 24,
      fontWeight: '600',
      lineHeight: 1.3,
      letterSpacing: 0,
    },
    h4: {
      fontSize: 20,
      fontWeight: '600',
      lineHeight: 1.3,
      letterSpacing: 0,
    },
    h5: {
      fontSize: 18,
      fontWeight: '600',
      lineHeight: 1.4,
      letterSpacing: 0,
    },
    h6: {
      fontSize: 16,
      fontWeight: '600',
      lineHeight: 1.4,
      letterSpacing: 0,
    },

    // Body Text
    bodyLarge: {
      fontSize: 18,
      fontWeight: '400',
      lineHeight: 1.6,
      letterSpacing: 0,
    },
    body: {
      fontSize: 16,
      fontWeight: '400',
      lineHeight: 1.5,
      letterSpacing: 0,
    },
    bodySmall: {
      fontSize: 14,
      fontWeight: '400',
      lineHeight: 1.4,
      letterSpacing: 0,
    },

    // Labels and Captions
    label: {
      fontSize: 14,
      fontWeight: '500',
      lineHeight: 1.4,
      letterSpacing: 0.5,
    },
    caption: {
      fontSize: 12,
      fontWeight: '400',
      lineHeight: 1.3,
      letterSpacing: 0.5,
    },
    overline: {
      fontSize: 12,
      fontWeight: '500',
      lineHeight: 1.3,
      letterSpacing: 1,
      textTransform: 'uppercase',
    },

    // Button Text
    button: {
      fontSize: 16,
      fontWeight: '600',
      lineHeight: 1.2,
      letterSpacing: 0.5,
    },
    buttonSmall: {
      fontSize: 14,
      fontWeight: '600',
      lineHeight: 1.2,
      letterSpacing: 0.5,
    },

    // Input Text
    input: {
      fontSize: 16,
      fontWeight: '400',
      lineHeight: 1.4,
      letterSpacing: 0,
    },
    inputLabel: {
      fontSize: 14,
      fontWeight: '500',
      lineHeight: 1.3,
      letterSpacing: 0,
    },
    inputError: {
      fontSize: 12,
      fontWeight: '400',
      lineHeight: 1.3,
      letterSpacing: 0,
    },
  },
};
