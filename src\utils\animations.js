/**
 * Animation Utilities - Reusable animation functions and configurations
 * Features: Common animations, easing functions, timing configurations
 */

import { Animated, Easing } from 'react-native';
import { theme } from '../theme';

// Easing functions
export const easings = {
  easeInOut: Easing.bezier(0.4, 0, 0.2, 1),
  easeOut: Easing.bezier(0, 0, 0.2, 1),
  easeIn: Easing.bezier(0.4, 0, 1, 1),
  spring: Easing.elastic(1.3),
  bounce: Easing.bounce,
};

// Animation configurations
export const animationConfigs = {
  fast: {
    duration: theme.animation.fast,
    easing: easings.easeOut,
    useNativeDriver: true,
  },
  normal: {
    duration: theme.animation.normal,
    easing: easings.easeInOut,
    useNativeDriver: true,
  },
  slow: {
    duration: theme.animation.slow,
    easing: easings.easeInOut,
    useNativeDriver: true,
  },
  spring: {
    tension: 100,
    friction: 8,
    useNativeDriver: true,
  },
  bounce: {
    tension: 180,
    friction: 12,
    useNativeDriver: true,
  },
};

// Fade animations
export const fadeIn = (animatedValue, config = animationConfigs.normal) => {
  return Animated.timing(animatedValue, {
    toValue: 1,
    ...config,
  });
};

export const fadeOut = (animatedValue, config = animationConfigs.normal) => {
  return Animated.timing(animatedValue, {
    toValue: 0,
    ...config,
  });
};

// Scale animations
export const scaleIn = (animatedValue, config = animationConfigs.spring) => {
  return Animated.spring(animatedValue, {
    toValue: 1,
    ...config,
  });
};

export const scaleOut = (animatedValue, config = animationConfigs.spring) => {
  return Animated.spring(animatedValue, {
    toValue: 0,
    ...config,
  });
};

// Slide animations
export const slideInFromRight = (animatedValue, distance = 100, config = animationConfigs.normal) => {
  return Animated.timing(animatedValue, {
    toValue: 0,
    ...config,
  });
};

export const slideInFromLeft = (animatedValue, distance = 100, config = animationConfigs.normal) => {
  return Animated.timing(animatedValue, {
    toValue: 0,
    ...config,
  });
};

export const slideInFromTop = (animatedValue, distance = 100, config = animationConfigs.normal) => {
  return Animated.timing(animatedValue, {
    toValue: 0,
    ...config,
  });
};

export const slideInFromBottom = (animatedValue, distance = 100, config = animationConfigs.normal) => {
  return Animated.timing(animatedValue, {
    toValue: 0,
    ...config,
  });
};

// Shake animation for error states
export const shakeAnimation = (animatedValue) => {
  return Animated.sequence([
    Animated.timing(animatedValue, {
      toValue: 10,
      duration: 50,
      useNativeDriver: true,
    }),
    Animated.timing(animatedValue, {
      toValue: -10,
      duration: 50,
      useNativeDriver: true,
    }),
    Animated.timing(animatedValue, {
      toValue: 10,
      duration: 50,
      useNativeDriver: true,
    }),
    Animated.timing(animatedValue, {
      toValue: -10,
      duration: 50,
      useNativeDriver: true,
    }),
    Animated.timing(animatedValue, {
      toValue: 0,
      duration: 50,
      useNativeDriver: true,
    }),
  ]);
};

// Pulse animation for loading states
export const pulseAnimation = (animatedValue) => {
  return Animated.loop(
    Animated.sequence([
      Animated.timing(animatedValue, {
        toValue: 1.1,
        duration: 800,
        easing: easings.easeInOut,
        useNativeDriver: true,
      }),
      Animated.timing(animatedValue, {
        toValue: 1,
        duration: 800,
        easing: easings.easeInOut,
        useNativeDriver: true,
      }),
    ])
  );
};

// Rotation animation
export const rotateAnimation = (animatedValue, duration = 1000) => {
  return Animated.loop(
    Animated.timing(animatedValue, {
      toValue: 1,
      duration,
      easing: Easing.linear,
      useNativeDriver: true,
    })
  );
};

// Stagger animation for multiple elements
export const staggerAnimation = (animations, staggerDelay = 100) => {
  return Animated.stagger(staggerDelay, animations);
};

// Parallel animation
export const parallelAnimation = (animations) => {
  return Animated.parallel(animations);
};

// Sequential animation
export const sequenceAnimation = (animations) => {
  return Animated.sequence(animations);
};

// Create interpolation for common transforms
export const createInterpolation = (animatedValue, inputRange, outputRange, extrapolate = 'clamp') => {
  return animatedValue.interpolate({
    inputRange,
    outputRange,
    extrapolate,
  });
};

// Common interpolations
export const createFadeInterpolation = (animatedValue) => {
  return createInterpolation(animatedValue, [0, 1], [0, 1]);
};

export const createScaleInterpolation = (animatedValue, minScale = 0.8, maxScale = 1) => {
  return createInterpolation(animatedValue, [0, 1], [minScale, maxScale]);
};

export const createSlideInterpolation = (animatedValue, distance = 100) => {
  return createInterpolation(animatedValue, [0, 1], [distance, 0]);
};

export const createRotateInterpolation = (animatedValue) => {
  return createInterpolation(animatedValue, [0, 1], ['0deg', '360deg']);
};

// Animation hooks for common use cases
export const useAnimatedValue = (initialValue = 0) => {
  return React.useRef(new Animated.Value(initialValue)).current;
};

export const useAnimatedValueXY = (initialValue = { x: 0, y: 0 }) => {
  return React.useRef(new Animated.ValueXY(initialValue)).current;
};

// Timing utilities
export const delay = (ms) => {
  return new Promise(resolve => setTimeout(resolve, ms));
};

export const animateWithDelay = (animation, delayMs = 0) => {
  return Animated.sequence([
    Animated.delay(delayMs),
    animation,
  ]);
};
