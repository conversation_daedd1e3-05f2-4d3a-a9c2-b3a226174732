/**
 * useForm Hook - Custom hook for form state management and validation
 * Features: Real-time validation, form submission, error handling
 */

import { useState, useCallback, useRef } from 'react';
import { validateForm } from '../utils/validation';

export const useForm = (initialValues = {}, validationRules = []) => {
  const [values, setValues] = useState(initialValues);
  const [errors, setErrors] = useState({});
  const [touched, setTouched] = useState({});
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isValid, setIsValid] = useState(false);
  
  // Keep track of validation rules
  const rulesRef = useRef(validationRules);
  rulesRef.current = validationRules;

  // Update a single field value
  const setValue = useCallback((field, value) => {
    setValues(prev => ({
      ...prev,
      [field]: value,
    }));

    // Real-time validation for touched fields
    if (touched[field]) {
      validateField(field, value);
    }
  }, [touched]);

  // Update multiple field values
  const setValues = useCallback((newValues) => {
    setValues(prev => ({
      ...prev,
      ...newValues,
    }));
  }, []);

  // Mark field as touched
  const setFieldTouched = useCallback((field, isTouched = true) => {
    setTouched(prev => ({
      ...prev,
      [field]: isTouched,
    }));

    // Validate field when it becomes touched
    if (isTouched) {
      validateField(field, values[field]);
    }
  }, [values]);

  // Validate a single field
  const validateField = useCallback((field, value) => {
    const fieldRules = rulesRef.current.filter(rule => rule.field === field);
    
    if (fieldRules.length === 0) {
      return { isValid: true, error: null };
    }

    const fieldData = { ...values, [field]: value };
    const result = validateForm(fieldData, fieldRules);
    
    setErrors(prev => ({
      ...prev,
      [field]: result.errors[field] || null,
    }));

    return {
      isValid: !result.errors[field],
      error: result.errors[field] || null,
    };
  }, [values]);

  // Validate entire form
  const validateAllFields = useCallback(() => {
    const result = validateForm(values, rulesRef.current);
    
    setErrors(result.errors);
    setIsValid(result.isValid);
    
    // Mark all fields as touched
    const allTouched = {};
    Object.keys(values).forEach(field => {
      allTouched[field] = true;
    });
    setTouched(allTouched);

    return result;
  }, [values]);

  // Handle form submission
  const handleSubmit = useCallback(async (onSubmit) => {
    setIsSubmitting(true);
    
    try {
      const validationResult = validateAllFields();
      
      if (validationResult.isValid) {
        await onSubmit(values);
      }
      
      return validationResult;
    } catch (error) {
      console.error('Form submission error:', error);
      throw error;
    } finally {
      setIsSubmitting(false);
    }
  }, [values, validateAllFields]);

  // Reset form to initial state
  const resetForm = useCallback(() => {
    setValues(initialValues);
    setErrors({});
    setTouched({});
    setIsSubmitting(false);
    setIsValid(false);
  }, [initialValues]);

  // Clear all errors
  const clearErrors = useCallback(() => {
    setErrors({});
  }, []);

  // Clear specific field error
  const clearFieldError = useCallback((field) => {
    setErrors(prev => ({
      ...prev,
      [field]: null,
    }));
  }, []);

  // Get field props for easy integration with input components
  const getFieldProps = useCallback((field) => {
    return {
      value: values[field] || '',
      onChangeText: (value) => setValue(field, value),
      onBlur: () => setFieldTouched(field, true),
      error: touched[field] ? errors[field] : null,
    };
  }, [values, errors, touched, setValue, setFieldTouched]);

  // Check if form has any errors
  const hasErrors = Object.values(errors).some(error => error !== null);

  // Check if form is ready to submit
  const canSubmit = isValid && !hasErrors && !isSubmitting;

  return {
    // Form state
    values,
    errors,
    touched,
    isSubmitting,
    isValid,
    hasErrors,
    canSubmit,

    // Form actions
    setValue,
    setValues,
    setFieldTouched,
    validateField,
    validateAllFields,
    handleSubmit,
    resetForm,
    clearErrors,
    clearFieldError,
    getFieldProps,
  };
};
